# 🚀 ACCESO RÁPIDO A TU VM UBUNTU-DEV

## ⚡ MÉTODO SÚPER RÁPIDO (1 comando)

```bash
./guia_acceso_vm.sh
```

---

## 📋 PASOS MANUALES

### 1️⃣ **INICIAR LA VM**
```bash
# Configurar libvirt
export LIBVIRT_DEFAULT_URI="qemu:///session"

# Iniciar VM
virsh start Ubuntu-Dev
```

### 2️⃣ **CONECTAR A LA VM**

#### 🌐 **OPCIÓN A: Acceso Web (Más Fácil)**

1. Abrir navegador
2. Ir a: **http://localhost:8080**
3. Seguir instrucciones

#### 🖥️ **OPCIÓN B: Cliente VNC (Recomendado)**
```bash
# Instalar cliente (solo la primera vez)
sudo pacman -S vinagre

# Conectar
vinagre vnc://127.0.0.1:5900
```

#### ⚡ **OPCIÓN C: Automático**
```bash
vinagre $(virsh domdisplay Ubuntu-Dev)
```

---

## 🔧 COMANDOS ÚTILES

| Acción | Comando |
|--------|---------|
| Ver estado | `virsh list` |
| Iniciar VM | `virsh start Ubuntu-Dev` |
| Apagar VM | `virsh shutdown Ubuntu-Dev` |
| Forzar apagado | `virsh destroy Ubuntu-Dev` |
| Info de conexión | `virsh domdisplay Ubuntu-Dev` |

---

## ⏱️ TIEMPOS DE ESPERA

- **Inicio de VM:** 10-15 segundos
- **Arranque de Lubuntu:** 30-60 segundos
- **Si ves pantalla negra:** Espera 1-2 minutos

---

## 🆘 SOLUCIÓN DE PROBLEMAS

### ❌ VM no inicia
```bash
./restart_vm_after_reboot.sh
```

### ❌ No puedo conectar
```bash
./setup_web_vnc.sh
```

### ❌ Pantalla de instalación
```bash
./fix_vm_boot_order.sh
```

---

## 🎯 FLUJO TÍPICO

1. **Abrir terminal** en `/home/<USER>/Tareas`
2. **Ejecutar:** `./guia_acceso_vm.sh`
3. **Esperar** a que termine el script
4. **Abrir navegador** en `http://localhost:8080`
5. **O instalar vinagre** y conectar directamente
6. **Esperar** 30-60 segundos a ver Lubuntu

---

## 💡 CONSEJOS

- **Primera vez:** Usa el acceso web para ver las instrucciones
- **Uso regular:** Instala vinagre para mejor rendimiento
- **Problemas:** Siempre ejecuta primero `./guia_acceso_vm.sh`
- **Después de reiniciar:** Ejecuta `./restart_vm_after_reboot.sh`

---

## 🎊 ¡DISFRUTA TU VM!

Tu VM Ubuntu-Dev con Lubuntu está lista para usar. 
¡Perfecto para desarrollo, pruebas y aprendizaje! 🖥️✨
