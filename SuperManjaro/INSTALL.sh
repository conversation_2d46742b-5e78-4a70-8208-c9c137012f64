#!/bin/bash

# 🚀 SuperManjaro - Instalador Automático
# Ejecuta todos los scripts de optimización en el orden correcto

echo "🚀 SUPERMANJARO - INSTALADOR AUTOMÁTICO"
echo "======================================"
echo ""
echo "Este script ejecutará todos los pasos de optimización automáticamente."
echo "Tiempo estimado: 15-20 minutos"
echo ""

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
RED='\033[0;31m'
NC='\033[0m'

show_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

show_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

show_error() {
    echo -e "${RED}✗${NC} $1"
}

# Verificar que estamos en el directorio correcto
if [ ! -f "optimize_manjaro.sh" ]; then
    show_error "Error: Ejecuta este script desde el directorio SuperManjaro"
    exit 1
fi

# Verificar permisos
if [ "$EUID" -eq 0 ]; then
    show_error "No ejecutes este script como root"
    exit 1
fi

# Función para ejecutar script con verificación
execute_script() {
    local script="$1"
    local description="$2"
    
    show_header "$description"
    
    if [ ! -f "$script" ]; then
        show_error "Script $script no encontrado"
        return 1
    fi
    
    chmod +x "$script"
    
    if ./"$script"; then
        show_message "$description completado exitosamente"
        return 0
    else
        show_error "$description falló"
        return 1
    fi
}

# Mostrar información del sistema
show_header "INFORMACIÓN DEL SISTEMA"
echo "Distribución: $(lsb_release -d | cut -f2)"
echo "Kernel: $(uname -r)"
echo "Memoria total: $(free -h | awk '/^Mem:/ {print $2}')"
echo "CPU: $(lscpu | grep 'Model name' | cut -d: -f2 | xargs)"
echo "Cores: $(nproc)"

# Confirmar instalación
echo ""
show_warning "¿Deseas continuar con la instalación completa de SuperManjaro? (s/N)"
read -r confirm
if [ "$confirm" != "s" ] && [ "$confirm" != "S" ]; then
    echo "Instalación cancelada"
    exit 0
fi

# Registro de tiempo de inicio
start_time=$(date +%s)

show_header "INICIANDO INSTALACIÓN SUPERMANJARO"

# Fase 1: Optimización Base del Sistema
execute_script "optimize_manjaro.sh" "FASE 1: Optimización Base del Sistema"
if [ $? -ne 0 ]; then
    show_error "Error en Fase 1. Revisa los logs y ejecuta manualmente."
    exit 1
fi

# Fase 2: Optimización de Navegadores
execute_script "optimize_browsers.sh" "FASE 2: Optimización de Navegadores"

# Fase 3: Optimizaciones Avanzadas
execute_script "complete_optimization.sh" "FASE 3: Optimizaciones Avanzadas"

# Verificar si VMware está instalado
show_header "VERIFICANDO VMWARE WORKSTATION PRO"
if command -v vmware &> /dev/null; then
    show_info "VMware Workstation Pro detectado"
    execute_script "vmware_optimization.sh" "FASE 4A: Optimización de VMware"
else
    show_warning "VMware Workstation Pro no detectado"
    show_info "Instala VMware manualmente y ejecuta: ./vmware_optimization.sh"
fi

# Fase 5: GNOME Boxes
execute_script "gnome_boxes_optimization.sh" "FASE 5: Configuración de GNOME Boxes"

# Fase 6: Plantillas de VMs
execute_script "vm_templates.sh" "FASE 6: Creación de Plantillas de VMs"

# Fase 7: Red y Almacenamiento
execute_script "vm_network_storage.sh" "FASE 7: Configuración de Red y Almacenamiento"

# Fase 8: Suite de Gestión
execute_script "vm_management_suite.sh" "FASE 8: Instalación de Suite de Gestión"

# Calcular tiempo total
end_time=$(date +%s)
total_time=$((end_time - start_time))
minutes=$((total_time / 60))
seconds=$((total_time % 60))

show_header "INSTALACIÓN COMPLETADA"
show_message "Tiempo total: ${minutes}m ${seconds}s"

echo ""
echo -e "${GREEN}🎉 SUPERMANJARO INSTALADO EXITOSAMENTE${NC}"
echo ""
echo -e "${BLUE}Próximos pasos:${NC}"
echo "1. Reinicia tu sesión para aplicar permisos de grupo"
echo "2. Ejecuta 'vm-suite' para comenzar a usar el sistema"
echo "3. Lee la documentación: cat Tutorial-SuperManjaro.md"
echo ""
echo -e "${BLUE}Scripts disponibles:${NC}"
echo "- vm-suite          # Suite principal interactiva"
echo "- vm-manager        # Gestión desde línea de comandos"
echo "- vm-monitor        # Monitoreo de recursos"
echo "- vm-backup         # Backup automático"
echo "- vm-cleanup        # Limpieza del sistema"
echo ""
echo -e "${BLUE}Documentación:${NC}"
echo "- README.md                    # Resumen del proyecto"
echo "- Tutorial-SuperManjaro.md     # Tutorial completo"
echo "- ~/.config/vm-suite/README.md # Documentación técnica"
echo ""
echo -e "${YELLOW}Nota importante:${NC}"
echo "Si VMware Workstation Pro no estaba instalado, instálalo manualmente"
echo "y luego ejecuta: ./vmware_optimization.sh"
echo ""
echo -e "${GREEN}¡Disfruta de tu SuperManjaro optimizado! 🚀${NC}"
