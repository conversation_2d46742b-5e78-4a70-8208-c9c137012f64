#!/bin/bash

# Script para conectar a una VM usando diferentes métodos

VM_NAME="$1"

if [ -z "$VM_NAME" ]; then
    echo "Uso: $0 <nombre_vm>"
    echo ""
    echo "VMs disponibles:"
    virsh list --name | grep -v "^$"
    exit 1
fi

# Verificar que la VM existe y está ejecutándose
if ! virsh list --name | grep -q "^${VM_NAME}$"; then
    echo "Error: VM '$VM_NAME' no está ejecutándose"
    echo ""
    echo "VMs ejecutándose:"
    virsh list --name | grep -v "^$"
    exit 1
fi

# Obtener información de conexión
DISPLAY_INFO=$(virsh domdisplay "$VM_NAME")

echo "🖥️  CONECTANDO A VM: $VM_NAME"
echo "================================"
echo ""
echo "Información de conexión: $DISPLAY_INFO"
echo ""

# Verificar si virt-viewer está disponible
if command -v virt-viewer &> /dev/null; then
    echo "✓ Usando virt-viewer (recomendado)"
    virt-viewer "$VM_NAME" &
elif command -v remote-viewer &> /dev/null; then
    echo "✓ Usando remote-viewer"
    remote-viewer "$DISPLAY_INFO" &
elif command -v spicy &> /dev/null; then
    echo "✓ Usando spicy (cliente SPICE)"
    spicy -h 127.0.0.1 -p 5900 &
else
    echo "⚠️  No se encontró cliente de visualización"
    echo ""
    echo "Opciones para conectar:"
    echo "1. Instalar virt-viewer: sudo pacman -S virt-viewer"
    echo "2. Usar cliente SPICE en: $DISPLAY_INFO"
    echo "3. Usar navegador web con noVNC (si está configurado)"
    echo ""
    echo "Para instalar virt-viewer:"
    echo "sudo pacman -S virt-viewer"
    echo ""
    echo "Luego ejecutar:"
    echo "virt-viewer $VM_NAME"
fi

echo ""
echo "ℹ️  Información adicional:"
echo "- Puerto SPICE: $(echo $DISPLAY_INFO | cut -d: -f3)"
echo "- Para detener la VM: vm-manager stop $VM_NAME"
echo "- Para ver estado: vm-manager list-vms"
