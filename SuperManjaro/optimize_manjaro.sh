#!/bin/bash

# Script de optimización para Manjaro Linux
# Creado para mejorar el rendimiento del sistema

echo "🚀 Iniciando optimización de Manjaro..."

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para mostrar mensajes
show_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

show_warning() {
    echo -e "${YELLOW}[ADVERTENCIA]${NC} $1"
}

show_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Limpiar caché del sistema
show_message "Limpiando caché del sistema..."
sudo pacman -Sc --noconfirm
sudo journalctl --vacuum-time=3d
sudo journalctl --vacuum-size=100M

# 2. Limpiar archivos temporales
show_message "Limpiando archivos temporales..."
sudo find /tmp -type f -atime +7 -delete 2>/dev/null
sudo find /var/tmp -type f -atime +7 -delete 2>/dev/null

# 3. Limpiar logs antiguos
show_message "Limpiando logs antiguos..."
sudo find /var/log -name "*.log" -type f -mtime +30 -delete 2>/dev/null

# 4. Optimizar base de datos de pacman
show_message "Optimizando base de datos de pacman..."
sudo pacman-db-upgrade

# 5. Actualizar locate database
show_message "Actualizando base de datos de locate..."
sudo updatedb

# 6. Limpiar caché de thumbnails
show_message "Limpiando caché de thumbnails..."
rm -rf ~/.cache/thumbnails/*

# 7. Limpiar caché de aplicaciones
show_message "Limpiando caché de aplicaciones..."
rm -rf ~/.cache/mozilla/firefox/*/cache2/*
rm -rf ~/.cache/google-chrome/*/Cache/*
rm -rf ~/.cache/brave/*/Cache/*

# 8. Verificar y reparar sistema de archivos (solo si es necesario)
show_message "Verificando integridad del sistema..."
sudo fsck -f /dev/nvme0n1p2 2>/dev/null || show_warning "No se pudo verificar el sistema de archivos"

# 9. Optimizar memoria
show_message "Liberando memoria caché..."
sync
echo 3 | sudo tee /proc/sys/vm/drop_caches > /dev/null

# 10. Mostrar estadísticas finales
show_message "Optimización completada!"
echo ""
echo -e "${BLUE}=== ESTADÍSTICAS DEL SISTEMA ===${NC}"
echo -e "Memoria libre: $(free -h | awk '/^Mem:/ {print $7}')"
echo -e "Espacio en disco libre: $(df -h / | awk 'NR==2 {print $4}')"
echo -e "Carga del sistema: $(uptime | awk -F'load average:' '{print $2}')"
echo ""
show_message "¡Sistema optimizado exitosamente!"
