#!/bin/bash

# Script para optimizar red y almacenamiento de VMs para 40GB RAM
# Configuraciones avanzadas para máximo rendimiento

echo "🌐 OPTIMIZANDO RED Y ALMACENAMIENTO PARA VMs"
echo "==========================================="

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

show_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

show_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Verificar permisos
if [ "$EUID" -eq 0 ]; then
    show_warning "No ejecutes este script como root"
    exit 1
fi

show_header "CONFIGURANDO REDES VIRTUALES OPTIMIZADAS"

# Red NAT optimizada para desarrollo
show_info "Creando red NAT optimizada para desarrollo..."

sudo tee /tmp/vm-nat-dev.xml << 'EOF'
<network>
  <name>vm-nat-dev</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr2' stp='off' delay='0'/>
  <mac address='52:54:00:12:34:57'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='**************0'/>
      <host mac='52:54:00:12:34:58' name='dev-vm-1' ip='**************'/>
      <host mac='52:54:00:12:34:59' name='dev-vm-2' ip='*************1'/>
    </dhcp>
  </ip>
</network>
EOF

# Red bridge para servidores
show_info "Creando red bridge para servidores..."

sudo tee /tmp/vm-bridge-srv.xml << 'EOF'
<network>
  <name>vm-bridge-srv</name>
  <forward mode='bridge'/>
  <bridge name='virbr3'/>
</network>
EOF

# Red aislada para testing
show_info "Creando red aislada para testing..."

sudo tee /tmp/vm-isolated-test.xml << 'EOF'
<network>
  <name>vm-isolated-test</name>
  <bridge name='virbr4' stp='off' delay='0'/>
  <mac address='52:54:00:12:34:60'/>
  <ip address='********' netmask='*************'>
    <dhcp>
      <range start='********0' end='**********'/>
    </dhcp>
  </ip>
</network>
EOF

# Definir y activar las redes
for network_file in /tmp/vm-nat-dev.xml /tmp/vm-bridge-srv.xml /tmp/vm-isolated-test.xml; do
    if [ -f "$network_file" ]; then
        network_name=$(basename "$network_file" .xml)
        sudo virsh net-define "$network_file" 2>/dev/null || true
        sudo virsh net-autostart "${network_name#vm-}" 2>/dev/null || true
        sudo virsh net-start "${network_name#vm-}" 2>/dev/null || true
    fi
done

show_message "Redes virtuales optimizadas creadas"

show_header "CONFIGURANDO ALMACENAMIENTO OPTIMIZADO"

# Pool de almacenamiento SSD para VMs de alto rendimiento
show_info "Creando pool SSD para alto rendimiento..."

sudo mkdir -p /home/<USER>/VMs/ssd-pool
sudo chown $USER:$USER /home/<USER>/VMs/ssd-pool

sudo tee /tmp/vm-ssd-pool.xml << 'EOF'
<pool type='dir'>
  <name>vm-ssd-pool</name>
  <target>
    <path>/home/<USER>/VMs/ssd-pool</path>
    <permissions>
      <mode>0755</mode>
      <owner>1000</owner>
      <group>1000</group>
    </permissions>
  </target>
</pool>
EOF

# Pool de almacenamiento para desarrollo
show_info "Creando pool para desarrollo..."

sudo mkdir -p /home/<USER>/VMs/dev-pool
sudo chown $USER:$USER /home/<USER>/VMs/dev-pool

sudo tee /tmp/vm-dev-pool.xml << 'EOF'
<pool type='dir'>
  <name>vm-dev-pool</name>
  <target>
    <path>/home/<USER>/VMs/dev-pool</path>
    <permissions>
      <mode>0755</mode>
      <owner>1000</owner>
      <group>1000</group>
    </permissions>
  </target>
</pool>
EOF

# Pool de almacenamiento para testing
show_info "Creando pool para testing..."

sudo mkdir -p /home/<USER>/VMs/test-pool
sudo chown $USER:$USER /home/<USER>/VMs/test-pool

sudo tee /tmp/vm-test-pool.xml << 'EOF'
<pool type='dir'>
  <name>vm-test-pool</name>
  <target>
    <path>/home/<USER>/VMs/test-pool</path>
    <permissions>
      <mode>0755</mode>
      <owner>1000</owner>
      <group>1000</group>
    </permissions>
  </target>
</pool>
EOF

# Definir y activar los pools
for pool_file in /tmp/vm-ssd-pool.xml /tmp/vm-dev-pool.xml /tmp/vm-test-pool.xml; do
    if [ -f "$pool_file" ]; then
        pool_name=$(basename "$pool_file" .xml)
        sudo virsh pool-define "$pool_file" 2>/dev/null || true
        sudo virsh pool-autostart "${pool_name#vm-}" 2>/dev/null || true
        sudo virsh pool-start "${pool_name#vm-}" 2>/dev/null || true
    fi
done

show_message "Pools de almacenamiento optimizados creados"

show_header "CONFIGURANDO OPTIMIZACIONES DE RENDIMIENTO"

# Configurar parámetros de red optimizados
show_info "Aplicando optimizaciones de red..."

sudo tee /etc/sysctl.d/99-vm-network.conf << 'EOF'
# Optimizaciones de red para VMs

# Configuración de buffer de red
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216

# Configuración TCP
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr

# Configuración de bridge
net.bridge.bridge-nf-call-iptables = 0
net.bridge.bridge-nf-call-ip6tables = 0
net.bridge.bridge-nf-call-arptables = 0

# Configuración de forwarding
net.ipv4.ip_forward = 1
net.ipv6.conf.all.forwarding = 1
EOF

# Aplicar configuraciones
sudo sysctl -p /etc/sysctl.d/99-vm-network.conf 2>/dev/null || true

show_message "Optimizaciones de red aplicadas"

# Crear script de optimización de discos VM
show_info "Creando script de optimización de discos..."

cat > ~/.local/bin/optimize-vm-disk << 'EOF'
#!/bin/bash

# Script para optimizar discos de VMs
# Uso: optimize-vm-disk <vm_name> [pool_name]

if [ $# -lt 1 ]; then
    echo "Uso: $0 <vm_name> [pool_name]"
    echo "Pools disponibles: ssd-pool, dev-pool, test-pool"
    exit 1
fi

VM_NAME="$1"
POOL_NAME="${2:-dev-pool}"

# Colores
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}Optimizando disco de VM: $VM_NAME${NC}"

# Verificar que la VM existe
if ! virsh dominfo "$VM_NAME" &>/dev/null; then
    echo "Error: VM '$VM_NAME' no encontrada"
    exit 1
fi

# Obtener información del disco
DISK_PATH=$(virsh domblklist "$VM_NAME" | grep vda | awk '{print $2}')

if [ -z "$DISK_PATH" ]; then
    echo "Error: No se pudo encontrar el disco de la VM"
    exit 1
fi

echo "Disco encontrado: $DISK_PATH"

# Verificar que la VM esté apagada
if [ "$(virsh domstate "$VM_NAME")" != "shut off" ]; then
    echo "Error: La VM debe estar apagada para optimizar el disco"
    exit 1
fi

# Optimizar el disco (compactar)
echo "Compactando disco..."
qemu-img convert -O qcow2 -c "$DISK_PATH" "${DISK_PATH}.optimized"

# Reemplazar el disco original
mv "$DISK_PATH" "${DISK_PATH}.backup"
mv "${DISK_PATH}.optimized" "$DISK_PATH"

echo -e "${GREEN}✓ Disco optimizado exitosamente${NC}"
echo "Backup creado: ${DISK_PATH}.backup"
EOF

chmod +x ~/.local/bin/optimize-vm-disk
show_message "Script de optimización de discos creado"

# Crear script de monitoreo de VMs
show_info "Creando script de monitoreo..."

cat > ~/.local/bin/vm-monitor << 'EOF'
#!/bin/bash

# Script de monitoreo de VMs
# Muestra estadísticas de uso de recursos

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}=== MONITOREO DE VMs ===${NC}"
echo ""

# Mostrar VMs activas
echo -e "${CYAN}VMs Activas:${NC}"
virsh list --state-running --name | while read vm; do
    if [ -n "$vm" ]; then
        # Obtener estadísticas de CPU y memoria
        cpu_time=$(virsh domstats "$vm" | grep "cpu.time" | cut -d= -f2)
        memory_actual=$(virsh domstats "$vm" | grep "balloon.current" | cut -d= -f2)
        memory_max=$(virsh domstats "$vm" | grep "balloon.maximum" | cut -d= -f2)
        
        if [ -n "$memory_actual" ] && [ -n "$memory_max" ]; then
            memory_percent=$((memory_actual * 100 / memory_max))
            echo "  $vm - Memoria: ${memory_percent}% ($(($memory_actual/1024))MB/$(($memory_max/1024))MB)"
        else
            echo "  $vm - Activa"
        fi
    fi
done

echo ""

# Mostrar uso de pools de almacenamiento
echo -e "${CYAN}Pools de Almacenamiento:${NC}"
for pool in ssd-pool dev-pool test-pool vm-storage; do
    if virsh pool-info "$pool" &>/dev/null; then
        capacity=$(virsh pool-info "$pool" | grep "Capacity:" | awk '{print $2 $3}')
        available=$(virsh pool-info "$pool" | grep "Available:" | awk '{print $2 $3}')
        echo "  $pool - Capacidad: $capacity, Disponible: $available"
    fi
done

echo ""

# Mostrar redes activas
echo -e "${CYAN}Redes Virtuales:${NC}"
virsh net-list --all | tail -n +3 | while read line; do
    if [ -n "$line" ]; then
        echo "  $line"
    fi
done
EOF

chmod +x ~/.local/bin/vm-monitor
show_message "Script de monitoreo creado"

# Limpiar archivos temporales
sudo rm -f /tmp/vm-*.xml

show_header "CONFIGURACIÓN COMPLETADA"
show_info "Redes optimizadas: vm-nat-dev, vm-bridge-srv, vm-isolated-test"
show_info "Pools de almacenamiento: ssd-pool, dev-pool, test-pool"
show_info "Scripts disponibles:"
show_info "  - optimize-vm-disk: Optimizar discos de VMs"
show_info "  - vm-monitor: Monitorear recursos de VMs"

echo ""
echo -e "${GREEN}🎉 RED Y ALMACENAMIENTO OPTIMIZADOS${NC}"
echo -e "${BLUE}Usa 'vm-monitor' para monitorear tus VMs${NC}"
