# 🚀 Guía de Instalación Completa - VM Ubuntu R-Exams ICFES

## 📋 Resumen del Proceso

1. **Descargar Ubuntu 22.04 LTS ISO**
2. **Crear VM en VMware Workstation**
3. **Instalar Ubuntu básico**
4. **Transferir scripts a la VM**
5. **Ejecutar instalación automática**
6. **¡VM lista para usar!**

---

## 1️⃣ **Descargar Ubuntu 22.04 LTS**

### **Enlace de Descarga:**
```
https://ubuntu.com/download/desktop
```

### **Archivo a descargar:**
- `ubuntu-22.04.3-desktop-amd64.iso` (aproximadamente 4.7 GB)
- Verificar checksum SHA256 si es posible

---

## 2️⃣ **Crear VM en VMware Workstation**

### **Configuración Inicial:**
1. **Abrir VMware Workstation**
2. **File → New Virtual Machine**
3. **<PERSON><PERSON>cciona<PERSON> "Custom (advanced)"**

### **Configuración Paso a Paso:**

#### **Hardware Compatibility:**
- Workstation 17.x (o la versión más reciente disponible)

#### **Guest Operating System:**
- **I will install the operating system later**
- Guest OS: **Linux**
- Version: **Ubuntu 64-bit**

#### **Virtual Machine Name:**
```
Name: Ubuntu-ICFES-RExams
Location: C:\VMware-VMs\Ubuntu-ICFES-RExams\
```

#### **Processor Configuration:**
```
Number of processors: 1
Number of cores per processor: 4-6
✅ Virtualize Intel VT-x/EPT or AMD-V/RVI
```

#### **Memory:**
```
Recommended: 8192 MB (8 GB)
Minimum: 6144 MB (6 GB)
Optimal: 12288 MB (12 GB)
```

#### **Network:**
```
Use network address translation (NAT)
```

#### **I/O Controller:**
```
LSI Logic (Recommended)
```

#### **Virtual Disk:**
```
Create a new virtual disk
Virtual disk type: SCSI (Recommended)
Disk size: 120 GB
✅ Split virtual disk into multiple files
```

#### **Configuraciones Avanzadas:**
```
CD/DVD: Use ISO image file → Seleccionar ubuntu-22.04.3-desktop-amd64.iso
USB Controller: USB 3.1
Sound Card: Auto detect
Display: ✅ Accelerate 3D graphics (256 MB video memory)
```

---

## 3️⃣ **Instalar Ubuntu 22.04 LTS**

### **Arrancar la VM:**
1. **Power on** la VM
2. Arrancar desde la ISO de Ubuntu

### **Proceso de Instalación:**

#### **Pantalla de Bienvenida:**
- Idioma: **Español** (o English si prefieres)
- **Instalar Ubuntu**

#### **Teclado:**
- Distribución: **Spanish** (o la que uses)

#### **Actualizaciones y Software:**
- ✅ **Instalación normal**
- ✅ **Descargar actualizaciones durante la instalación**
- ✅ **Instalar software de terceros**

#### **Tipo de Instalación:**
- **Borrar disco e instalar Ubuntu**
- **Instalar ahora**

#### **Zona Horaria:**
- Seleccionar tu zona horaria (ej: America/Bogota)

#### **Usuario y Contraseña:**
```
Nombre: Tu Nombre
Nombre del equipo: ubuntu-icfes-rexams
Nombre de usuario: icfes (o el que prefieras)
Contraseña: [contraseña segura]
✅ Iniciar sesión automáticamente
```

#### **Finalizar Instalación:**
- Esperar a que termine la instalación
- **Reiniciar ahora**
- Retirar la ISO cuando se solicite

---

## 4️⃣ **Configuración Post-Instalación**

### **Primer Arranque:**
1. **Iniciar sesión** en Ubuntu
2. **Completar configuración inicial** (cuentas online, etc.)
3. **Saltar** configuraciones opcionales

### **Actualizaciones Iniciales:**
```bash
sudo apt update
sudo apt upgrade -y
sudo reboot
```

### **Instalar VMware Tools:**
```bash
sudo apt install open-vm-tools open-vm-tools-desktop -y
sudo reboot
```

---

## 5️⃣ **Transferir Scripts a la VM**

### **Opción 1: Carpetas Compartidas VMware**
1. **VM → Settings → Options → Shared Folders**
2. **Always enabled**
3. **Add** → Seleccionar carpeta que contiene "Ubuntu-R-exams"
4. En Ubuntu: acceder a `/mnt/hgfs/[nombre-carpeta]/`

### **Opción 2: Transferencia Manual**
1. **Comprimir** la carpeta "Ubuntu-R-exams" en tu host
2. **Transferir** via USB, red, o descarga web
3. **Descomprimir** en el home del usuario Ubuntu

### **Opción 3: Git (si tienes repositorio)**
```bash
git clone [tu-repositorio] ~/Ubuntu-R-exams
```

---

## 6️⃣ **Ejecutar Instalación Automática**

### **En la VM Ubuntu:**
```bash
# Ir al directorio de scripts
cd ~/Ubuntu-R-exams

# Hacer ejecutable el script maestro
chmod +x install-complete-vm.sh

# Ejecutar instalación completa
./install-complete-vm.sh
```

### **Proceso Automático:**
- ⏱️ **Duración:** 30-60 minutos
- 📦 **Descarga:** Varios GB de paquetes
- 🔄 **Progreso:** Se muestra en pantalla
- 📝 **Log:** Se guarda automáticamente

### **Al Finalizar:**
```bash
# Reiniciar el sistema
sudo reboot

# Acceder al menú principal
rexams-menu
```

---

## 7️⃣ **Verificación Final**

### **Comandos de Verificación:**
```bash
# Información del sistema
system-info.sh

# Probar LaTeX
test-latex.sh

# Probar Python
test-python-integration.sh

# Crear ejercicio de prueba
new_rexams prueba_instalacion
```

### **Estructura Final:**
```
/home/<USER>/
├── Proyectos/RepositorioMatematicasICFES_R_Exams/
├── Recursos/Plantillas/
├── Scripts/
├── R/
├── Python-Envs/
└── Backup/
```

---

## 🎯 **Comandos Útiles Post-Instalación**

```bash
rexams-menu              # Menú principal
rexams                   # Ir al proyecto
new_rexams nombre        # Crear ejercicio
compile-rexams.sh file   # Compilar
manage-templates.sh list # Ver plantillas
backup-rexams.sh         # Backup
system-monitor.sh        # Monitor sistema
```

---

## 🔧 **Troubleshooting**

### **Si la instalación falla:**
1. Verificar conexión a internet
2. Revisar logs en `~/installation-logs/`
3. Ejecutar scripts individuales
4. Verificar espacio en disco

### **Si VMware Tools no funciona:**
```bash
sudo apt install open-vm-tools open-vm-tools-desktop
sudo systemctl enable vmtoolsd
sudo reboot
```

### **Si falta memoria:**
- Aumentar RAM asignada a la VM
- Cerrar aplicaciones innecesarias durante instalación

---

## ✅ **Lista de Verificación**

- [ ] Ubuntu 22.04 LTS descargado
- [ ] VM creada con especificaciones correctas
- [ ] Ubuntu instalado y actualizado
- [ ] VMware Tools funcionando
- [ ] Scripts transferidos a la VM
- [ ] Instalación automática ejecutada
- [ ] Sistema reiniciado
- [ ] Verificaciones pasadas
- [ ] Primer ejercicio creado

---

## 🎉 **¡Listo!**

Tu VM Ubuntu R-Exams ICFES está completamente configurada y lista para crear contenido matemático profesional para el ICFES.

**Próximos pasos:**
1. Explorar plantillas disponibles
2. Crear tu primer ejercicio real
3. Configurar Git para tu proyecto
4. ¡Comenzar a desarrollar contenido increíble!

---

**Tiempo total estimado:** 2-3 horas (incluyendo descargas)  
**Dificultad:** Fácil (proceso mayormente automático)  
**Resultado:** VM completamente funcional para R-Exams ICFES
