# 🚀 VM Ubuntu 22.04 LTS - RepositorioMatematicasICFES_R_Exams

## 📋 Descripción General

Esta máquina virtual está específicamente configurada para el desarrollo de contenido matemático para el ICFES utilizando R-Exams. Incluye un entorno completo con R, RStudio, LaTeX, Python científico y todas las herramientas necesarias para crear ejercicios matemáticos interactivos.

## 🎯 Características Principales

### **Stack Tecnológico Completo**
- ✅ **Ubuntu 22.04 LTS** - Sistema operativo estable y confiable
- ✅ **R 4.3+** con RStudio Desktop - Entorno de desarrollo estadístico
- ✅ **LaTeX completo** - TeX Live con paquetes matemáticos y TikZ
- ✅ **Python científico** - NumPy, Pandas, Matplotlib, SciPy, SymPy
- ✅ **VMware Tools** - Integración optimizada con VMware Workstation
- ✅ **Git** - Control de versiones
- ✅ **Visual Studio Code** - Editor de código moderno

### **Configuración Especializada para R-Exams**
- 📦 **Paquete r-exams** con todas las dependencias
- 🎨 **TikZ optimizado** para gráficos matemáticos
- 🐍 **Integración Python-R** via reticulate
- 📝 **Plantillas predefinidas** para diferentes tipos de ejercicios
- 🔧 **Scripts de automatización** para compilación y gestión

## 🛠️ Instalación y Configuración

### **Paso 1: Crear la VM en VMware Workstation**

1. **Configuración recomendada:**
   - RAM: 8-12 GB
   - CPU: 4-6 cores virtuales
   - Almacenamiento: 100-120 GB
   - Gráficos: Aceleración 3D habilitada

2. **Instalar Ubuntu 22.04 LTS** con las especificaciones del archivo `VM-Ubuntu-RepositorioICFES-Specs.md`

### **Paso 2: Ejecutar Scripts de Instalación**

```bash
# 1. Hacer ejecutables todos los scripts
chmod +x *.sh

# 2. Instalación base del entorno
./install-icfes-rexams-environment.sh

# 3. Reiniciar el sistema
sudo reboot

# 4. Configurar entorno R específico
./configure-r-environment.sh

# 5. Crear plantillas y ejemplos
./create-rexams-templates.sh

# 6. Optimizar configuración LaTeX
./optimize-latex-config.sh

# 7. Configurar Python científico
./setup-python-scientific.sh

# 8. Aplicar optimizaciones finales
./final-optimizations.sh

# 9. Reiniciar nuevamente
sudo reboot
```

### **Paso 3: Verificar Instalación**

```bash
# Probar configuración LaTeX
bash ~/Scripts/test-latex.sh

# Probar integración Python
bash ~/Scripts/test-python-integration.sh

# Ver información del sistema
bash ~/Scripts/system-info.sh
```

## 🎛️ Uso del Sistema

### **Menú Principal**
```bash
# Acceder al menú principal de herramientas
rexams-menu
```

### **Comandos Rápidos**
```bash
# Ir al directorio del proyecto
rexams

# Crear nuevo ejercicio
new_rexams nombre_ejercicio

# Compilar ejercicio
compile-rexams.sh archivo.Rmd

# Gestionar plantillas
manage-templates.sh list

# Abrir RStudio en el proyecto
rstudio-icfes

# Monitor del sistema
system-monitor.sh

# Crear backup
backup-rexams.sh
```

## 📁 Estructura de Directorios

```
/home/<USER>/
├── Proyectos/
│   ├── RepositorioMatematicasICFES_R_Exams/    # Proyecto principal
│   ├── Plantillas/                             # Plantillas personalizadas
│   └── Ejemplos/                               # Ejemplos de ejercicios
├── Recursos/
│   ├── LaTeX-Templates/                        # Plantillas LaTeX
│   ├── R-Libraries/                            # Bibliotecas R adicionales
│   ├── Python-Scripts/                         # Scripts Python
│   └── Plantillas/                             # Plantillas por materia
│       ├── Algebra/
│       ├── Geometria/
│       ├── Estadistica/
│       ├── Calculo/
│       └── Trigonometria/
├── Scripts/                                    # Scripts de automatización
├── R/                                          # Configuraciones R
├── Python-Envs/                               # Entornos virtuales Python
└── Backup/                                     # Backups automáticos
```

## 🔧 Configuraciones Importantes

### **Archivos de Configuración**
- `~/.Rprofile` - Configuración personalizada de R
- `~/R/python-config.R` - Integración Python-R
- `~/R/tikz-config.R` - Configuración TikZ
- `~/texmf/tex/latex/local/rexams-icfes.sty` - Paquete LaTeX personalizado

### **Variables de Entorno**
```bash
export R_LIBS_USER="~/R/library"
export TEXINPUTS=".:~/texmf/tex/latex//:"
export PATH="$PATH:~/Scripts"
```

## 📚 Plantillas Disponibles

### **Álgebra**
- `ecuacion_lineal_basica.Rmd` - Ecuaciones lineales con aleatorización

### **Estadística**
- `grafico_barras_interpretacion.Rmd` - Interpretación de gráficos con Python

### **Geometría**
- `area_triangulo_tikz.Rmd` - Cálculo de áreas con TikZ

## 🐍 Integración Python

### **Configuración**
```r
# En R, cargar configuración Python
source("~/R/python-config.R")
check_python_packages()
import_python_modules()
setup_matplotlib()
```

### **Ejemplo de Uso**
```r
# Generar gráfico con Python en R-Exams
py_run_string("
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 100)
y = np.sin(x)

plt.figure(figsize=(8, 5))
plt.plot(x, y)
plt.savefig('grafico.png', dpi=150)
plt.close()
")
```

## 💾 Sistema de Backup

### **Backup Automático**
- **Frecuencia:** Semanal (domingos 2:00 AM)
- **Ubicación:** `~/Backup/`
- **Retención:** Últimos 5 backups

### **Backup Manual**
```bash
backup-rexams.sh
```

## 📊 Monitoreo del Sistema

### **Monitor en Tiempo Real**
```bash
system-monitor.sh
```

### **Información del Sistema**
```bash
system-info.sh
```

## 🔍 Troubleshooting

### **Problemas Comunes**

#### **R-Exams no compila**
```bash
# Verificar configuración R
R -e "library(exams); sessionInfo()"

# Verificar LaTeX
test-latex.sh

# Reinstalar paquetes si es necesario
R -e "install.packages('exams', dependencies = TRUE)"
```

#### **Python no funciona en R**
```bash
# Verificar integración
test-python-integration.sh

# Reconfigurar Python
source ~/Python-Envs/rexams-icfes/bin/activate
R -e "library(reticulate); py_config()"
```

#### **TikZ no genera gráficos**
```bash
# Verificar LaTeX
pdflatex --version

# Probar TikZ
R -e "source('~/R/tikz-config.R'); setup_tikz()"
```

#### **Rendimiento lento**
```bash
# Verificar recursos
system-monitor.sh

# Optimizar VM
# - Aumentar RAM asignada
# - Habilitar aceleración 3D
# - Verificar VMware Tools
```

## 🔄 Actualizaciones

### **Sistema**
```bash
sudo apt update && sudo apt upgrade
```

### **Paquetes R**
```bash
R -e "update.packages(ask = FALSE)"
```

### **Paquetes Python**
```bash
source ~/Python-Envs/rexams-icfes/bin/activate
pip install --upgrade pip
pip list --outdated | cut -d' ' -f1 | xargs pip install --upgrade
```

## 📞 Soporte

### **Logs del Sistema**
- Backup: `~/backup.log`
- Sistema: `/var/log/syslog`
- Aplicaciones: `~/.local/share/`

### **Comandos de Diagnóstico**
```bash
# Estado general
system-info.sh

# Verificar servicios
systemctl status vmtoolsd

# Verificar espacio en disco
df -h

# Verificar memoria
free -h
```

## 🎉 ¡Listo para Usar!

Tu VM está completamente configurada para desarrollar contenido matemático para el ICFES usando R-Exams. 

**Próximos pasos:**
1. Ejecuta `rexams-menu` para acceder al menú principal
2. Crea tu primer ejercicio con `new_rexams mi_ejercicio`
3. Explora las plantillas disponibles con `manage-templates.sh list`
4. ¡Comienza a crear contenido matemático increíble!

## 📋 Lista de Verificación Post-Instalación

### **Verificación Básica**
- [ ] Ubuntu 22.04 LTS instalado y actualizado
- [ ] VMware Tools funcionando correctamente
- [ ] Conexión a internet estable
- [ ] Usuario con permisos sudo configurado

### **Verificación de Software**
- [ ] R y RStudio funcionando
- [ ] LaTeX compilando correctamente
- [ ] Python científico instalado
- [ ] Integración Python-R operativa
- [ ] Git configurado con usuario

### **Verificación de Scripts**
- [ ] Todos los scripts ejecutables
- [ ] Menú principal funcionando
- [ ] Sistema de backup configurado
- [ ] Plantillas disponibles

### **Verificación Final**
- [ ] Crear ejercicio de prueba
- [ ] Compilar a PDF exitosamente
- [ ] Generar gráfico con Python
- [ ] Backup manual funcional

---

**Versión:** 1.0
**Fecha:** Enero 2024
**Compatibilidad:** Ubuntu 22.04 LTS, VMware Workstation 17.x
**Mantenido por:** RepositorioMatematicasICFES_R_Exams
