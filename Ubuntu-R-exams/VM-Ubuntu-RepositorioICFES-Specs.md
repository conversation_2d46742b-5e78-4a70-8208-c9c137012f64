# VM Ubuntu 22.04 LTS - RepositorioMatematicasICFES_R_Exams

## 🎯 Especificaciones de Hardware Recomendadas

### **Configuración Mínima**
- **RAM**: 6 GB
- **CPU**: 4 cores virtuales
- **Almacenamiento**: 60 GB
- **Gráficos**: 128 MB VRAM

### **Configuración Óptima (Recomendada)**
- **RAM**: 8-12 GB
- **CPU**: 6-8 cores virtuales
- **Almacenamiento**: 100-120 GB SSD
- **Gráficos**: 256 MB VRAM con aceleración 3D

### **Configuración Profesional**
- **RAM**: 16 GB
- **CPU**: 8-12 cores virtuales
- **Almacenamiento**: 150-200 GB NVMe
- **Gráficos**: 512 MB VRAM con aceleración 3D

## 🔧 Configuración de VMware Workstation

### **Configuración Inicial de la VM**
```
Nombre: Ubuntu-ICFES-RExams
Ubicación: ~/VMware-VMs/Ubuntu-ICFES-RExams/
Sistema Operativo: Linux - Ubuntu 64-bit
Compatibilidad: Workstation 17.x
```

### **Hardware Configuration**
```
Memoria: 8192 MB (8 GB)
Procesadores: 4 cores, 2 cores por socket
Virtualización: Intel VT-x/EPT habilitado
Disco Duro: 100 GB, SCSI, Thin Provisioned
Red: NAT (para desarrollo) o Bridged (para servidor)
USB: USB 3.1
Sonido: Auto detect
Display: Acelerar gráficos 3D habilitado
```

### **Configuraciones Avanzadas**
```
Memoria compartida para gráficos: 256 MB
Habilitar IOMMU: Sí
Habilitar VBS: No (para mejor rendimiento)
Firmware: UEFI
Secure Boot: Deshabilitado
```

## 💾 Esquema de Particionado Recomendado

### **Particionado para 100 GB**
```
/boot/efi    - 512 MB  (FAT32)
/boot        - 1 GB    (ext4)
/            - 40 GB   (ext4)
/home        - 50 GB   (ext4)
/var         - 6 GB    (ext4)
swap         - 2 GB    (swap)
```

### **Justificación del Particionado**
- **`/boot/efi`**: UEFI boot loader
- **`/boot`**: Kernels y archivos de arranque
- **`/`**: Sistema base Ubuntu
- **`/home`**: Proyectos R-Exams y datos de usuario
- **`/var`**: Logs, cache de paquetes, bases de datos
- **`swap`**: Memoria virtual (2GB suficiente con 8GB RAM)

## 🚀 Configuración Post-Instalación

### **Actualizaciones Iniciales**
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install curl wget git vim nano htop tree -y
```

### **Configuración de Idioma y Región**
```bash
sudo apt install language-pack-es
sudo locale-gen es_ES.UTF-8
sudo update-locale LANG=es_ES.UTF-8
```

### **Instalación de Herramientas Básicas**
```bash
sudo apt install build-essential software-properties-common apt-transport-https -y
sudo apt install ca-certificates gnupg lsb-release -y
```

## 🔐 Configuración de Seguridad

### **Firewall Básico**
```bash
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
```

### **Usuario de Desarrollo**
```bash
# Crear usuario específico para desarrollo (opcional)
sudo adduser icfes-dev
sudo usermod -aG sudo icfes-dev
```

## 📁 Estructura de Directorios Recomendada

```
/home/<USER>/
├── Proyectos/
│   ├── RepositorioMatematicasICFES_R_Exams/
│   ├── Plantillas/
│   └── Ejemplos/
├── Recursos/
│   ├── LaTeX-Templates/
│   ├── R-Libraries/
│   └── Python-Scripts/
├── Documentos/
│   ├── Manuales/
│   └── Referencias/
└── Backup/
    ├── Proyectos/
    └── Configuraciones/
```

## ⚡ Optimizaciones de Rendimiento

### **Configuración de Swap**
```bash
# Reducir swappiness para mejor rendimiento con suficiente RAM
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
```

### **Optimizaciones de Disco**
```bash
# Habilitar TRIM para SSD (si aplica)
sudo systemctl enable fstrim.timer
```

### **Configuración de VMware Tools**
```bash
# Instalar VMware Tools para mejor integración
sudo apt install open-vm-tools open-vm-tools-desktop -y
sudo systemctl enable vmtoolsd
```

## 🌐 Configuración de Red

### **Para Desarrollo Local (NAT)**
- Acceso a internet completo
- Aislamiento de la red local
- Port forwarding si necesario

### **Para Servidor de Desarrollo (Bridged)**
- IP propia en la red local
- Acceso directo desde otros dispositivos
- Ideal para testing colaborativo

## 📊 Monitoreo y Mantenimiento

### **Herramientas de Monitoreo**
```bash
sudo apt install htop iotop nethogs ncdu -y
```

### **Scripts de Mantenimiento**
```bash
# Limpieza automática semanal
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

## 🔄 Backup y Snapshots

### **Estrategia de Snapshots**
1. **Snapshot inicial**: Después de instalación base
2. **Snapshot configurado**: Después de instalar todo el stack
3. **Snapshots de trabajo**: Antes de cambios importantes
4. **Snapshot estable**: Versión funcional para producción

### **Backup de Datos**
```bash
# Script de backup automático (se creará más adelante)
mkdir -p ~/Scripts
# backup-icfes.sh se creará en el siguiente paso
```

## 📝 Notas Importantes

1. **Memoria**: Con 8GB RAM, la VM puede manejar cómodamente R + RStudio + LaTeX
2. **CPU**: 4-6 cores virtuales son suficientes para compilación de documentos
3. **Almacenamiento**: 100GB permiten amplio espacio para proyectos y cache
4. **Red**: NAT es suficiente para desarrollo, Bridged para colaboración
5. **Snapshots**: Crear snapshots frecuentes durante configuración inicial

## 🎯 Próximos Pasos

Una vez creada la VM con estas especificaciones, procederemos con:

1. Instalación automática del stack R-Exams
2. Configuración de LaTeX completo
3. Setup de Python científico
4. Configuraciones y optimizaciones finales
