#!/bin/bash

# ============================================================================
# Script de Configuración R-Exams - RepositorioMatematicasICFES_R_Exams
# Configuración específica de R, paquetes y entorno de desarrollo
# ============================================================================

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

log "🔧 Configurando entorno R específico para R-Exams..."

# ============================================================================
# CONFIGURACIÓN DE R Y PAQUETES ESENCIALES
# ============================================================================

log "📦 Instalando paquetes R esenciales para r-exams..."

# Crear directorio para bibliotecas de usuario
mkdir -p ~/R/library

# Script R para instalar paquetes
cat > /tmp/install_r_packages.R << 'EOF'
# Configurar repositorio CRAN
options(repos = c(CRAN = "https://cran.r-project.org/"))

# Función para instalar paquetes con manejo de errores
install_safe <- function(packages) {
  for (pkg in packages) {
    if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
      cat("Instalando:", pkg, "\n")
      tryCatch({
        install.packages(pkg, dependencies = TRUE)
        cat("✅ Instalado:", pkg, "\n")
      }, error = function(e) {
        cat("❌ Error instalando", pkg, ":", e$message, "\n")
      })
    } else {
      cat("✅ Ya instalado:", pkg, "\n")
    }
  }
}

# Paquetes esenciales para r-exams
essential_packages <- c(
  "exams",           # Paquete principal r-exams
  "knitr",           # Para documentos dinámicos
  "rmarkdown",       # Markdown para R
  "reticulate",      # Integración con Python
  "tikzDevice",      # Gráficos TikZ
  "digest",          # Funciones hash
  "testthat",        # Testing
  "devtools",        # Herramientas de desarrollo
  "roxygen2",        # Documentación
  "usethis"          # Utilidades de desarrollo
)

# Paquetes matemáticos y estadísticos
math_packages <- c(
  "ggplot2",         # Gráficos avanzados
  "plotly",          # Gráficos interactivos
  "dplyr",           # Manipulación de datos
  "tidyr",           # Datos ordenados
  "stringr",         # Manipulación de strings
  "lubridate",       # Fechas y tiempos
  "readr",           # Lectura de datos
  "readxl",          # Lectura de Excel
  "writexl",         # Escritura de Excel
  "DT",              # Tablas interactivas
  "kableExtra"       # Tablas mejoradas
)

# Paquetes para matemáticas específicas
specialized_packages <- c(
  "Ryacas",          # Álgebra simbólica
  "pracma",          # Matemáticas prácticas
  "polynom",         # Polinomios
  "combinat",        # Combinatoria
  "gtools",          # Herramientas generales
  "MASS",            # Estadística moderna aplicada
  "lattice",         # Gráficos lattice
  "gridExtra",       # Extensiones de grid
  "RColorBrewer",    # Paletas de colores
  "viridis"          # Paletas de colores científicas
)

# Instalar paquetes por categorías
cat("=== INSTALANDO PAQUETES ESENCIALES ===\n")
install_safe(essential_packages)

cat("\n=== INSTALANDO PAQUETES MATEMÁTICOS ===\n")
install_safe(math_packages)

cat("\n=== INSTALANDO PAQUETES ESPECIALIZADOS ===\n")
install_safe(specialized_packages)

# Verificar instalación de exams
if (require("exams", quietly = TRUE)) {
  cat("\n✅ Paquete 'exams' instalado correctamente\n")
  cat("Versión:", as.character(packageVersion("exams")), "\n")
} else {
  cat("\n❌ Error: Paquete 'exams' no se pudo instalar\n")
}

cat("\n🎉 Instalación de paquetes R completada\n")
EOF

# Ejecutar script de instalación de paquetes R
Rscript /tmp/install_r_packages.R

log "✅ Paquetes R instalados"

# ============================================================================
# CONFIGURACIÓN DE ARCHIVOS DE CONFIGURACIÓN
# ============================================================================

log "⚙️ Configurando archivos de configuración..."

# Crear .Rprofile personalizado
cat > ~/.Rprofile << 'EOF'
# ============================================================================
# .Rprofile para RepositorioMatematicasICFES_R_Exams
# ============================================================================

# Configuración de repositorios
options(repos = c(CRAN = "https://cran.r-project.org/"))

# Configuración de TikZ para r-exams
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}",
  "\\usepackage[spanish]{babel}",
  "\\usepackage[utf8]{inputenc}"
))

# Configuración numérica
Sys.setlocale("LC_NUMERIC", "C")
options(scipen = 999)
options(OutDec = ".")

# Configuración de encoding
options(encoding = "UTF-8")

# Configuración de memoria y rendimiento
options(max.print = 1000)
options(width = 120)

# Cargar bibliotecas comunes automáticamente
.First <- function() {
  cat("🚀 Entorno R-Exams ICFES cargado\n")
  cat("📁 Directorio de trabajo:", getwd(), "\n")
  cat("📦 Paquetes disponibles: exams, knitr, rmarkdown, reticulate\n")
  cat("🔧 Configuración TikZ lista para r-exams\n\n")
}

# Función de ayuda personalizada
icfes_help <- function() {
  cat("=== AYUDA RÁPIDA R-EXAMS ICFES ===\n")
  cat("📚 Comandos útiles:\n")
  cat("  - library(exams)           # Cargar r-exams\n")
  cat("  - exams2pdf()              # Generar PDF\n")
  cat("  - exams2moodle()           # Generar para Moodle\n")
  cat("  - exams2html()             # Generar HTML\n")
  cat("  - setwd('~/Proyectos/')    # Cambiar directorio\n")
  cat("  - list.files()             # Listar archivos\n")
  cat("📁 Directorios importantes:\n")
  cat("  - ~/Proyectos/RepositorioMatematicasICFES_R_Exams/\n")
  cat("  - ~/Recursos/\n")
  cat("  - ~/Scripts/\n")
}
EOF

# Configurar variables de entorno en .bashrc
cat >> ~/.bashrc << 'EOF'

# ============================================================================
# Configuración para RepositorioMatematicasICFES_R_Exams
# ============================================================================

# Variables de entorno para R
export R_LIBS_USER="~/R/library"
export R_PROFILE_USER="~/.Rprofile"

# Variables para LaTeX
export TEXINPUTS=".:~/texmf/tex/latex//:"
export PATH="$PATH:~/bin:~/Scripts"

# Aliases útiles
alias rexams='cd ~/Proyectos/RepositorioMatematicasICFES_R_Exams'
alias recursos='cd ~/Recursos'
alias rstudio-icfes='cd ~/Proyectos/RepositorioMatematicasICFES_R_Exams && rstudio .'
alias r-help='R -e "icfes_help()"'

# Función para crear nuevo ejercicio r-exams
new_rexams() {
    if [ -z "$1" ]; then
        echo "Uso: new_rexams nombre_ejercicio"
        return 1
    fi
    
    local nombre="$1"
    local archivo="~/Proyectos/RepositorioMatematicasICFES_R_Exams/${nombre}.Rmd"
    
    if [ -f "$archivo" ]; then
        echo "❌ El archivo $archivo ya existe"
        return 1
    fi
    
    # Crear plantilla básica
    cat > "$archivo" << 'TEMPLATE'
---
output:
  pdf_document:
    keep_tex: true
  html_document: default
---

```{r setup, include=FALSE}
library(exams)
library(knitr)
options(OutDec = ".")
```

```{r variables, echo=FALSE, results='hide'}
# Definir variables aleatorias aquí
```

Question
========

[Escribir la pregunta aquí]

Answerlist
----------
- Opción A
- Opción B  
- Opción C
- Opción D

Solution
========

[Escribir la solución aquí]

Answerlist
----------
- Verdadero
- Falso
- Falso
- Falso

Meta-information
================
exname: NOMBRE_EJERCICIO
extype: schoice
exsolution: 1000
exshuffle: TRUE
TEMPLATE

    echo "✅ Creado: $archivo"
    echo "📝 Puedes editarlo con: code $archivo"
}

echo "🔧 Configuración R-Exams cargada"
EOF

log "✅ Archivos de configuración creados"

# ============================================================================
# CREAR SCRIPTS ÚTILES
# ============================================================================

log "📝 Creando scripts útiles..."

# Script para compilar ejercicios r-exams
cat > ~/Scripts/compile-rexams.sh << 'EOF'
#!/bin/bash
# Script para compilar ejercicios r-exams

if [ -z "$1" ]; then
    echo "Uso: compile-rexams.sh archivo.Rmd [formato]"
    echo "Formatos disponibles: pdf, html, moodle, all"
    exit 1
fi

archivo="$1"
formato="${2:-pdf}"

if [ ! -f "$archivo" ]; then
    echo "❌ Archivo no encontrado: $archivo"
    exit 1
fi

echo "🔄 Compilando $archivo en formato $formato..."

case $formato in
    pdf)
        R -e "library(exams); exams2pdf('$archivo')"
        ;;
    html)
        R -e "library(exams); exams2html('$archivo')"
        ;;
    moodle)
        R -e "library(exams); exams2moodle('$archivo')"
        ;;
    all)
        R -e "library(exams); exams2pdf('$archivo'); exams2html('$archivo'); exams2moodle('$archivo')"
        ;;
    *)
        echo "❌ Formato no reconocido: $formato"
        exit 1
        ;;
esac

echo "✅ Compilación completada"
EOF

chmod +x ~/Scripts/compile-rexams.sh

log "✅ Scripts útiles creados"

# ============================================================================
# INFORMACIÓN FINAL
# ============================================================================

echo ""
echo -e "${BLUE}============================================================================${NC}"
echo -e "${BLUE}  CONFIGURACIÓN R-EXAMS COMPLETADA${NC}"
echo -e "${BLUE}============================================================================${NC}"
echo ""
echo -e "${GREEN}✅ Paquetes R esenciales instalados${NC}"
echo -e "${GREEN}✅ Configuración .Rprofile personalizada${NC}"
echo -e "${GREEN}✅ Variables de entorno configuradas${NC}"
echo -e "${GREEN}✅ Scripts útiles creados${NC}"
echo ""
echo -e "${YELLOW}🚀 COMANDOS ÚTILES:${NC}"
echo "  rexams                    # Ir al directorio del proyecto"
echo "  new_rexams nombre         # Crear nuevo ejercicio"
echo "  compile-rexams.sh file.Rmd # Compilar ejercicio"
echo "  rstudio-icfes             # Abrir RStudio en el proyecto"
echo "  r-help                    # Mostrar ayuda rápida"
echo ""
echo -e "${BLUE}📁 Para empezar:${NC}"
echo "1. cd ~/Proyectos/RepositorioMatematicasICFES_R_Exams"
echo "2. new_rexams mi_primer_ejercicio"
echo "3. code mi_primer_ejercicio.Rmd"
echo ""

log "🎉 ¡Configuración R-Exams completada exitosamente!"
