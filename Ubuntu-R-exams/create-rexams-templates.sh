#!/bin/bash

# ============================================================================
# Script para crear plantillas y ejemplos R-Exams ICFES
# Genera plantillas base para diferentes tipos de ejercicios matemáticos
# ============================================================================

set -e

GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log "📝 Creando plantillas y ejemplos para R-Exams ICFES..."

# Crear directorios para plantillas
mkdir -p ~/Recursos/Plantillas/{Algebra,Geometria,Estadistica,Calculo,Trigonometria}
mkdir -p ~/Proyectos/RepositorioMatematicasICFES_R_Exams/Ejemplos

# ============================================================================
# PLANTILLA BÁSICA DE ÁLGEBRA
# ============================================================================

cat > ~/Recursos/Plantillas/Algebra/ecuacion_lineal_basica.Rmd << 'EOF'
---
output:
  pdf_document:
    keep_tex: true
  html_document: default
---

```{r setup, include=FALSE}
library(exams)
library(knitr)
options(OutDec = ".")
Sys.setlocale("LC_NUMERIC", "C")
```

```{r variables, echo=FALSE, results='hide'}
# Aleatorización de coeficientes
set.seed(sample(1:10000, 1))

a <- sample(c(2:9), 1)
b <- sample(c(1:15), 1)
c <- sample(c(10:50), 1)

# Calcular la solución
x_solucion <- (c - b) / a

# Generar distractores
distractor1 <- round(x_solucion + sample(1:5, 1))
distractor2 <- round(x_solucion - sample(1:5, 1))
distractor3 <- round(x_solucion * sample(c(2, 0.5), 1))

# Mezclar opciones
opciones <- c(x_solucion, distractor1, distractor2, distractor3)
opciones_mezcladas <- sample(opciones)
indice_correcto <- which(opciones_mezcladas == x_solucion)

# Vector solución para r-exams
solucion <- rep(0, 4)
solucion[indice_correcto] <- 1
```

Question
========

Resuelve la siguiente ecuación lineal:

$$`r a`x + `r b` = `r c`$$

¿Cuál es el valor de $x$?

Answerlist
----------
- $x = `r opciones_mezcladas[1]`$
- $x = `r opciones_mezcladas[2]`$
- $x = `r opciones_mezcladas[3]`$
- $x = `r opciones_mezcladas[4]`$

Solution
========

Para resolver la ecuación $`r a`x + `r b` = `r c`$, seguimos estos pasos:

1. **Aislar el término con $x$:**
   $$`r a`x = `r c` - `r b`$$
   $$`r a`x = `r c - b`$$

2. **Dividir ambos lados por `r a`:**
   $$x = \frac{`r c - b`}{`r a`} = `r x_solucion`$$

**Verificación:**
$`r a` \cdot `r x_solucion` + `r b` = `r a * x_solucion` + `r b` = `r a * x_solucion + b` = `r c`$ ✓

Por lo tanto, $x = `r x_solucion`$.

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: ecuacion_lineal_basica
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Algebra|Ecuaciones lineales
EOF

# ============================================================================
# PLANTILLA DE ESTADÍSTICA CON GRÁFICO
# ============================================================================

cat > ~/Recursos/Plantillas/Estadistica/grafico_barras_interpretacion.Rmd << 'EOF'
---
output:
  pdf_document:
    keep_tex: true
  html_document: default
---

```{r setup, include=FALSE}
library(exams)
library(knitr)
library(reticulate)
options(OutDec = ".")
Sys.setlocale("LC_NUMERIC", "C")

knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)
```

```{r variables, echo=FALSE, results='hide'}
set.seed(sample(1:10000, 1))

# Aleatorización de datos
categorias <- c("Matemáticas", "Español", "Ciencias", "Sociales", "Inglés")
valores <- sample(60:95, 5)
names(valores) <- categorias

# Encontrar la materia con mayor puntaje
materia_mayor <- names(which.max(valores))
puntaje_mayor <- max(valores)

# Generar distractores
distractores <- sample(categorias[categorias != materia_mayor], 3)

# Mezclar opciones
opciones <- c(materia_mayor, distractores)
opciones_mezcladas <- sample(opciones)
indice_correcto <- which(opciones_mezcladas == materia_mayor)

solucion <- rep(0, 4)
solucion[indice_correcto] <- 1
```

```{r generar_grafico, echo=FALSE, results='hide'}
# Código Python para generar gráfico de barras
codigo_python <- paste0("
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

# Datos
categorias = ['", paste(categorias, collapse="', '"), "']
valores = [", paste(valores, collapse=", "), "]

# Crear gráfico
plt.figure(figsize=(10, 6))
barras = plt.bar(categorias, valores, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])

# Personalización
plt.title('Puntajes Promedio por Materia', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('Materias', fontsize=12, fontweight='bold')
plt.ylabel('Puntaje Promedio', fontsize=12, fontweight='bold')
plt.ylim(0, 100)

# Añadir valores en las barras
for i, v in enumerate(valores):
    plt.text(i, v + 1, str(v), ha='center', va='bottom', fontweight='bold')

# Mejorar apariencia
plt.grid(axis='y', alpha=0.3)
plt.tight_layout()

# Guardar
plt.savefig('grafico_barras.png', dpi=150, bbox_inches='tight')
plt.savefig('grafico_barras.pdf', dpi=150, bbox_inches='tight')
plt.close()
")

py_run_string(codigo_python)
```

Question
========

El siguiente gráfico muestra los puntajes promedio obtenidos por un grupo de estudiantes en diferentes materias del examen ICFES:

```{r mostrar_grafico, echo=FALSE, results='asis', fig.align="center"}
cat("![](grafico_barras.png){width=80%}")
```

Según el gráfico, ¿cuál es la materia en la que los estudiantes obtuvieron el **mayor puntaje promedio**?

Answerlist
----------
- `r opciones_mezcladas[1]`
- `r opciones_mezcladas[2]`
- `r opciones_mezcladas[3]`
- `r opciones_mezcladas[4]`

Solution
========

Para determinar la materia con mayor puntaje promedio, debemos observar cuál barra del gráfico tiene la mayor altura.

Analizando los datos del gráfico:

```{r mostrar_datos, echo=FALSE, results='asis'}
for(i in 1:length(categorias)) {
  cat("- **", categorias[i], "**: ", valores[i], " puntos\n")
}
```

**Comparación de puntajes:**
- El puntaje más alto es **`r puntaje_mayor` puntos**
- Este puntaje corresponde a la materia **`r materia_mayor`**

Por lo tanto, la materia en la que los estudiantes obtuvieron el mayor puntaje promedio es **`r materia_mayor`**.

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: interpretacion_grafico_barras
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Estadistica|Interpretacion de graficos
EOF

# ============================================================================
# PLANTILLA DE GEOMETRÍA CON TIKZ
# ============================================================================

cat > ~/Recursos/Plantillas/Geometria/area_triangulo_tikz.Rmd << 'EOF'
---
output:
  pdf_document:
    keep_tex: true
  html_document: default
---

```{r setup, include=FALSE}
library(exams)
library(knitr)
options(OutDec = ".")
Sys.setlocale("LC_NUMERIC", "C")
```

```{r variables, echo=FALSE, results='hide'}
set.seed(sample(1:10000, 1))

# Aleatorización de dimensiones
base <- sample(6:12, 1)
altura <- sample(4:10, 1)

# Calcular área correcta
area_correcta <- (base * altura) / 2

# Generar distractores
distractor1 <- base * altura  # Error común: no dividir por 2
distractor2 <- base + altura  # Error: sumar en lugar de multiplicar
distractor3 <- round(area_correcta * 1.5)  # Error de cálculo

# Mezclar opciones
opciones <- c(area_correcta, distractor1, distractor2, distractor3)
opciones_mezcladas <- sample(opciones)
indice_correcto <- which(opciones_mezcladas == area_correcta)

solucion <- rep(0, 4)
solucion[indice_correcto] <- 1
```

Question
========

Observa el siguiente triángulo:

```{r tikz_triangulo, echo=FALSE, results='asis', engine='tikz', fig.ext='png', fig.width=4, fig.height=3}
\begin{tikzpicture}[scale=0.8]
  % Dibujar triángulo
  \draw[thick] (0,0) -- (6,0) -- (3,4) -- cycle;
  
  % Etiquetas de dimensiones
  \draw[<->] (0,-0.5) -- (6,-0.5);
  \node at (3,-0.8) {`r base` cm};
  
  \draw[<->] (6.3,0) -- (6.3,4);
  \node at (6.8,2) {`r altura` cm};
  
  % Línea de altura (punteada)
  \draw[dashed] (3,0) -- (3,4);
  
  % Ángulo recto
  \draw (2.8,0) -- (2.8,0.2) -- (3,0.2);
\end{tikzpicture}
```

¿Cuál es el área del triángulo mostrado?

Answerlist
----------
- `r opciones_mezcladas[1]` cm²
- `r opciones_mezcladas[2]` cm²
- `r opciones_mezcladas[3]` cm²
- `r opciones_mezcladas[4]` cm²

Solution
========

Para calcular el área de un triángulo, utilizamos la fórmula:

$$\text{Área} = \frac{\text{base} \times \text{altura}}{2}$$

**Datos del problema:**
- Base = `r base` cm
- Altura = `r altura` cm

**Sustituyendo en la fórmula:**
$$\text{Área} = \frac{`r base` \times `r altura`}{2} = \frac{`r base * altura`}{2} = `r area_correcta` \text{ cm}^2$$

Por lo tanto, el área del triángulo es **`r area_correcta` cm²**.

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: area_triangulo
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Geometria|Areas
EOF

log "✅ Plantillas creadas en ~/Recursos/Plantillas/"

# ============================================================================
# CREAR SCRIPT DE GESTIÓN DE PLANTILLAS
# ============================================================================

cat > ~/Scripts/manage-templates.sh << 'EOF'
#!/bin/bash

# Script para gestionar plantillas R-Exams

show_help() {
    echo "=== GESTOR DE PLANTILLAS R-EXAMS ICFES ==="
    echo ""
    echo "Uso: manage-templates.sh [comando] [opciones]"
    echo ""
    echo "Comandos disponibles:"
    echo "  list                    - Listar todas las plantillas"
    echo "  copy [plantilla] [nombre] - Copiar plantilla a proyecto"
    echo "  create [tipo] [nombre]  - Crear nueva plantilla"
    echo "  help                    - Mostrar esta ayuda"
    echo ""
    echo "Tipos de plantilla:"
    echo "  algebra, geometria, estadistica, calculo, trigonometria"
    echo ""
    echo "Ejemplos:"
    echo "  manage-templates.sh list"
    echo "  manage-templates.sh copy ecuacion_lineal_basica mi_ejercicio"
    echo "  manage-templates.sh create algebra nueva_ecuacion"
}

list_templates() {
    echo "📁 Plantillas disponibles:"
    echo ""
    find ~/Recursos/Plantillas -name "*.Rmd" -type f | while read file; do
        categoria=$(basename $(dirname "$file"))
        nombre=$(basename "$file" .Rmd)
        echo "  [$categoria] $nombre"
    done
}

copy_template() {
    if [ -z "$1" ] || [ -z "$2" ]; then
        echo "❌ Uso: copy [plantilla] [nombre_nuevo]"
        return 1
    fi
    
    plantilla="$1"
    nuevo_nombre="$2"
    
    # Buscar la plantilla
    archivo_plantilla=$(find ~/Recursos/Plantillas -name "${plantilla}.Rmd" -type f | head -n1)
    
    if [ -z "$archivo_plantilla" ]; then
        echo "❌ Plantilla '$plantilla' no encontrada"
        return 1
    fi
    
    destino="~/Proyectos/RepositorioMatematicasICFES_R_Exams/${nuevo_nombre}.Rmd"
    
    if [ -f "$destino" ]; then
        echo "❌ El archivo $destino ya existe"
        return 1
    fi
    
    cp "$archivo_plantilla" "$destino"
    echo "✅ Plantilla copiada: $destino"
    echo "📝 Editar con: code $destino"
}

case "$1" in
    list)
        list_templates
        ;;
    copy)
        copy_template "$2" "$3"
        ;;
    help|"")
        show_help
        ;;
    *)
        echo "❌ Comando no reconocido: $1"
        show_help
        ;;
esac
EOF

chmod +x ~/Scripts/manage-templates.sh

log "✅ Script de gestión de plantillas creado"

echo ""
echo -e "${BLUE}============================================================================${NC}"
echo -e "${BLUE}  PLANTILLAS R-EXAMS ICFES CREADAS${NC}"
echo -e "${BLUE}============================================================================${NC}"
echo ""
echo -e "${GREEN}✅ Plantillas creadas:${NC}"
echo "  📐 Álgebra: ecuación lineal básica"
echo "  📊 Estadística: interpretación de gráfico de barras"
echo "  📏 Geometría: área de triángulo con TikZ"
echo ""
echo -e "${GREEN}✅ Script de gestión: ~/Scripts/manage-templates.sh${NC}"
echo ""
echo -e "${BLUE}🚀 Para usar las plantillas:${NC}"
echo "  manage-templates.sh list                    # Ver plantillas"
echo "  manage-templates.sh copy ecuacion_lineal_basica mi_ejercicio"
echo ""

log "🎉 ¡Plantillas R-Exams ICFES creadas exitosamente!"
