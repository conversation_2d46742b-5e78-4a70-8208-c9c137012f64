#!/bin/bash

# ============================================================================
# Script de Optimizaciones Finales - VM Ubuntu R-Exams ICFES
# Configuraciones finales, optimizaciones de rendimiento y herramientas útiles
# ============================================================================

set -e

GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

log "⚡ Aplicando optimizaciones finales para VM R-Exams ICFES..."

# ============================================================================
# OPTIMIZACIONES DE RENDIMIENTO DEL SISTEMA
# ============================================================================

log "🚀 Aplicando optimizaciones de rendimiento..."

# Configurar swappiness para mejor rendimiento con suficiente RAM
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf

# Optimizar I/O scheduler para SSD (si aplica)
echo 'ACTION=="add|change", KERNEL=="sd[a-z]*", ATTR{queue/rotational}=="0", ATTR{queue/scheduler}="mq-deadline"' | sudo tee -a /etc/udev/rules.d/60-ioschedulers.rules

# Habilitar TRIM para SSD
sudo systemctl enable fstrim.timer

# Configurar límites de archivos abiertos para R/LaTeX
echo '* soft nofile 65536' | sudo tee -a /etc/security/limits.conf
echo '* hard nofile 65536' | sudo tee -a /etc/security/limits.conf

log "✅ Optimizaciones de rendimiento aplicadas"

# ============================================================================
# CONFIGURAR HERRAMIENTAS DE MONITOREO
# ============================================================================

log "📊 Configurando herramientas de monitoreo..."

# Instalar herramientas de monitoreo
sudo apt install -y htop iotop nethogs ncdu tree neofetch

# Crear script de monitoreo del sistema
cat > ~/Scripts/system-monitor.sh << 'EOF'
#!/bin/bash

# Script de monitoreo del sistema para VM R-Exams

echo "🖥️  ESTADO DEL SISTEMA - VM R-EXAMS ICFES"
echo "========================================"
echo ""

# Información básica del sistema
echo "📅 Fecha: $(date)"
echo "⏰ Uptime: $(uptime -p)"
echo ""

# Uso de CPU
echo "🔥 USO DE CPU:"
top -bn1 | grep "Cpu(s)" | awk '{print "   " $2 " usuario, " $4 " sistema, " $8 " idle"}'
echo ""

# Uso de memoria
echo "💾 USO DE MEMORIA:"
free -h | awk 'NR==2{printf "   Usado: %s/%s (%.1f%%)\n", $3,$2,$3*100/$2 }'
echo ""

# Uso de disco
echo "💿 USO DE DISCO:"
df -h | grep -E '^/dev/' | awk '{printf "   %s: %s/%s (%s)\n", $6, $3, $2, $5}'
echo ""

# Procesos que más consumen CPU
echo "🔥 TOP 5 PROCESOS (CPU):"
ps aux --sort=-%cpu | head -6 | tail -5 | awk '{printf "   %s: %.1f%% CPU\n", $11, $3}'
echo ""

# Procesos que más consumen memoria
echo "💾 TOP 5 PROCESOS (MEMORIA):"
ps aux --sort=-%mem | head -6 | tail -5 | awk '{printf "   %s: %.1f%% MEM\n", $11, $4}'
echo ""

# Estado de servicios importantes
echo "⚙️  SERVICIOS:"
services=("vmtoolsd" "ssh" "cron")
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo "   ✅ $service: activo"
    else
        echo "   ❌ $service: inactivo"
    fi
done
echo ""

# Espacio en directorios importantes
echo "📁 ESPACIO EN DIRECTORIOS:"
dirs=("$HOME/Proyectos" "$HOME/Recursos" "/tmp")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        size=$(du -sh "$dir" 2>/dev/null | cut -f1)
        echo "   $dir: $size"
    fi
done
EOF

chmod +x ~/Scripts/system-monitor.sh

log "✅ Herramientas de monitoreo configuradas"

# ============================================================================
# CONFIGURAR BACKUP AUTOMÁTICO
# ============================================================================

log "💾 Configurando sistema de backup..."

# Crear script de backup
cat > ~/Scripts/backup-rexams.sh << 'EOF'
#!/bin/bash

# Script de backup para proyectos R-Exams ICFES

BACKUP_DIR="$HOME/Backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="rexams-backup-$DATE"

echo "💾 Iniciando backup de R-Exams ICFES..."

# Crear directorio de backup si no existe
mkdir -p "$BACKUP_DIR"

# Crear archivo de backup
cd "$HOME"
tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" \
    --exclude='*.tmp' \
    --exclude='*.log' \
    --exclude='.git' \
    --exclude='__pycache__' \
    Proyectos/RepositorioMatematicasICFES_R_Exams \
    Recursos \
    R \
    Scripts \
    .Rprofile \
    2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Backup creado: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
    
    # Mostrar tamaño del backup
    size=$(du -sh "$BACKUP_DIR/$BACKUP_NAME.tar.gz" | cut -f1)
    echo "📊 Tamaño del backup: $size"
    
    # Limpiar backups antiguos (mantener solo los últimos 5)
    cd "$BACKUP_DIR"
    ls -t rexams-backup-*.tar.gz | tail -n +6 | xargs -r rm
    
    echo "🧹 Backups antiguos limpiados (manteniendo últimos 5)"
else
    echo "❌ Error creando backup"
    exit 1
fi

echo "🎉 Backup completado exitosamente"
EOF

chmod +x ~/Scripts/backup-rexams.sh

# Configurar cron para backup automático semanal
(crontab -l 2>/dev/null; echo "0 2 * * 0 $HOME/Scripts/backup-rexams.sh >> $HOME/backup.log 2>&1") | crontab -

log "✅ Sistema de backup configurado (semanal, domingos 2:00 AM)"

# ============================================================================
# CREAR MENÚ PRINCIPAL DE HERRAMIENTAS
# ============================================================================

log "🎛️ Creando menú principal de herramientas..."

cat > ~/Scripts/rexams-menu.sh << 'EOF'
#!/bin/bash

# Menú principal de herramientas R-Exams ICFES

GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

show_header() {
    clear
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}           MENÚ PRINCIPAL - REPOSITORIO MATEMÁTICAS ICFES R-EXAMS${NC}"
    echo -e "${BLUE}============================================================================${NC}"
    echo ""
}

show_menu() {
    echo -e "${GREEN}📋 OPCIONES DISPONIBLES:${NC}"
    echo ""
    echo "  1) 🚀 Ir al proyecto R-Exams"
    echo "  2) 📝 Crear nuevo ejercicio"
    echo "  3) 🔧 Compilar ejercicio"
    echo "  4) 📊 Abrir RStudio"
    echo "  5) 🐍 Probar integración Python"
    echo "  6) 📄 Gestionar plantillas"
    echo "  7) 💾 Crear backup"
    echo "  8) 📊 Monitor del sistema"
    echo "  9) 🧪 Ejecutar pruebas"
    echo " 10) ⚙️  Configuración"
    echo " 11) 📚 Ayuda"
    echo "  0) 🚪 Salir"
    echo ""
}

while true; do
    show_header
    show_menu
    
    read -p "Selecciona una opción [0-11]: " choice
    
    case $choice in
        1)
            echo "🚀 Abriendo proyecto R-Exams..."
            cd ~/Proyectos/RepositorioMatematicasICFES_R_Exams
            bash
            ;;
        2)
            read -p "📝 Nombre del nuevo ejercicio: " nombre
            if [ ! -z "$nombre" ]; then
                new_rexams "$nombre"
            fi
            read -p "Presiona Enter para continuar..."
            ;;
        3)
            echo "🔧 Archivos .Rmd disponibles:"
            find ~/Proyectos/RepositorioMatematicasICFES_R_Exams -name "*.Rmd" -type f
            read -p "Archivo a compilar: " archivo
            if [ -f "$archivo" ]; then
                ~/Scripts/compile-rexams.sh "$archivo"
            fi
            read -p "Presiona Enter para continuar..."
            ;;
        4)
            echo "📊 Abriendo RStudio..."
            cd ~/Proyectos/RepositorioMatematicasICFES_R_Exams
            rstudio . &
            ;;
        5)
            echo "🐍 Probando integración Python..."
            ~/Scripts/test-python-integration.sh
            read -p "Presiona Enter para continuar..."
            ;;
        6)
            echo "📄 Gestión de plantillas..."
            ~/Scripts/manage-templates.sh help
            read -p "Presiona Enter para continuar..."
            ;;
        7)
            echo "💾 Creando backup..."
            ~/Scripts/backup-rexams.sh
            read -p "Presiona Enter para continuar..."
            ;;
        8)
            echo "📊 Monitor del sistema..."
            ~/Scripts/system-monitor.sh
            read -p "Presiona Enter para continuar..."
            ;;
        9)
            echo "🧪 Ejecutando pruebas..."
            ~/Scripts/test-latex.sh
            ~/Scripts/test-python-integration.sh
            read -p "Presiona Enter para continuar..."
            ;;
        10)
            echo "⚙️  Configuración del sistema..."
            echo "📁 Archivos de configuración:"
            echo "  - ~/.Rprofile"
            echo "  - ~/R/python-config.R"
            echo "  - ~/R/tikz-config.R"
            read -p "Presiona Enter para continuar..."
            ;;
        11)
            echo -e "${YELLOW}📚 AYUDA RÁPIDA:${NC}"
            echo ""
            echo "🔧 Comandos útiles:"
            echo "  rexams                    # Ir al directorio del proyecto"
            echo "  new_rexams nombre         # Crear nuevo ejercicio"
            echo "  compile-rexams.sh file.Rmd # Compilar ejercicio"
            echo "  manage-templates.sh list  # Ver plantillas"
            echo ""
            echo "📁 Directorios importantes:"
            echo "  ~/Proyectos/RepositorioMatematicasICFES_R_Exams/"
            echo "  ~/Recursos/Plantillas/"
            echo "  ~/Scripts/"
            echo ""
            read -p "Presiona Enter para continuar..."
            ;;
        0)
            echo "🚪 ¡Hasta luego!"
            exit 0
            ;;
        *)
            echo "❌ Opción no válida"
            read -p "Presiona Enter para continuar..."
            ;;
    esac
done
EOF

chmod +x ~/Scripts/rexams-menu.sh

# Crear alias para acceso rápido al menú
echo "alias rexams-menu='~/Scripts/rexams-menu.sh'" >> ~/.bashrc

log "✅ Menú principal creado"

# ============================================================================
# CONFIGURAR ACTUALIZACIONES AUTOMÁTICAS
# ============================================================================

log "🔄 Configurando actualizaciones automáticas..."

# Configurar unattended-upgrades
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Configurar actualizaciones de seguridad automáticas
echo 'Unattended-Upgrade::Automatic-Reboot "false";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades
echo 'Unattended-Upgrade::Remove-Unused-Dependencies "true";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades

log "✅ Actualizaciones automáticas configuradas"

# ============================================================================
# CREAR SCRIPT DE INFORMACIÓN DEL SISTEMA
# ============================================================================

log "ℹ️ Creando script de información del sistema..."

cat > ~/Scripts/system-info.sh << 'EOF'
#!/bin/bash

# Script de información del sistema VM R-Exams ICFES

echo "🖥️  INFORMACIÓN DEL SISTEMA"
echo "=========================="
echo ""

# Información básica
echo "💻 Sistema: $(lsb_release -d | cut -f2)"
echo "🏗️  Arquitectura: $(uname -m)"
echo "🔧 Kernel: $(uname -r)"
echo "💾 RAM Total: $(free -h | awk 'NR==2{print $2}')"
echo "💿 Espacio en disco: $(df -h / | awk 'NR==2{print $2}')"
echo ""

# Información de software
echo "📦 SOFTWARE INSTALADO:"
echo "  🔢 R: $(R --version | head -n1 | cut -d' ' -f3)"
echo "  🐍 Python: $(python3 --version | cut -d' ' -f2)"
echo "  📝 LaTeX: $(pdflatex --version | head -n1 | cut -d' ' -f2)"
echo "  📊 RStudio: $(rstudio --version 2>/dev/null | cut -d' ' -f2 || echo 'Instalado')"
echo ""

# Información de R-Exams
echo "📚 ENTORNO R-EXAMS:"
R -e "
if (require('exams', quietly = TRUE)) {
  cat('  ✅ Paquete exams:', as.character(packageVersion('exams')), '\n')
} else {
  cat('  ❌ Paquete exams no instalado\n')
}

if (require('reticulate', quietly = TRUE)) {
  cat('  ✅ Integración Python configurada\n')
} else {
  cat('  ❌ Integración Python no configurada\n')
}
" 2>/dev/null

echo ""

# Información de directorios
echo "📁 ESTRUCTURA DE DIRECTORIOS:"
dirs=("$HOME/Proyectos" "$HOME/Recursos" "$HOME/Scripts" "$HOME/R")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        count=$(find "$dir" -type f | wc -l)
        echo "  📂 $dir: $count archivos"
    fi
done

echo ""
echo "🎉 Sistema R-Exams ICFES configurado y listo para usar"
EOF

chmod +x ~/Scripts/system-info.sh

log "✅ Script de información del sistema creado"

# ============================================================================
# HACER TODOS LOS SCRIPTS EJECUTABLES
# ============================================================================

log "🔧 Configurando permisos de scripts..."

find ~/Scripts -name "*.sh" -type f -exec chmod +x {} \;

log "✅ Permisos de scripts configurados"

# ============================================================================
# INFORMACIÓN FINAL
# ============================================================================

echo ""
echo -e "${BLUE}============================================================================${NC}"
echo -e "${BLUE}  OPTIMIZACIONES FINALES COMPLETADAS${NC}"
echo -e "${BLUE}============================================================================${NC}"
echo ""
echo -e "${GREEN}✅ Optimizaciones de rendimiento aplicadas${NC}"
echo -e "${GREEN}✅ Herramientas de monitoreo configuradas${NC}"
echo -e "${GREEN}✅ Sistema de backup automático configurado${NC}"
echo -e "${GREEN}✅ Menú principal de herramientas creado${NC}"
echo -e "${GREEN}✅ Actualizaciones automáticas configuradas${NC}"
echo ""
echo -e "${BLUE}🎛️ ACCESO RÁPIDO:${NC}"
echo "  rexams-menu               # Menú principal de herramientas"
echo "  system-monitor.sh         # Monitor del sistema"
echo "  system-info.sh            # Información del sistema"
echo "  backup-rexams.sh          # Crear backup manual"
echo ""
echo -e "${BLUE}📁 SCRIPTS CREADOS:${NC}"
echo "  ~/Scripts/system-monitor.sh"
echo "  ~/Scripts/backup-rexams.sh"
echo "  ~/Scripts/rexams-menu.sh"
echo "  ~/Scripts/system-info.sh"
echo ""
echo -e "${YELLOW}🔄 REINICIO RECOMENDADO:${NC}"
echo "  sudo reboot"
echo ""

log "🎉 ¡Todas las optimizaciones completadas exitosamente!"
