#!/bin/bash

# ============================================================================
# SCRIPT MAESTRO - INSTALACIÓN COMPLETA VM UBUNTU R-EXAMS ICFES
# Ejecuta toda la configuración de forma automática y secuencial
# ============================================================================

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Función para logging con colores
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

success() {
    echo -e "${CYAN}[SUCCESS] $1${NC}"
}

# Función para mostrar progreso
show_progress() {
    local current=$1
    local total=$2
    local description=$3
    local percentage=$((current * 100 / total))
    
    echo ""
    echo -e "${PURPLE}============================================================================${NC}"
    echo -e "${PURPLE}  PROGRESO: $current/$total ($percentage%) - $description${NC}"
    echo -e "${PURPLE}============================================================================${NC}"
    echo ""
}

# Función para verificar si es Ubuntu
check_ubuntu() {
    if ! grep -q "Ubuntu" /etc/os-release; then
        error "Este script requiere Ubuntu 22.04 LTS"
    fi
    
    local version=$(lsb_release -rs)
    if [[ "$version" != "22.04" ]]; then
        warn "Se recomienda Ubuntu 22.04 LTS. Versión detectada: $version"
        read -p "¿Continuar de todos modos? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Función para verificar recursos del sistema
check_system_resources() {
    local ram_gb=$(free -g | awk 'NR==2{print $2}')
    local disk_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    
    info "Verificando recursos del sistema..."
    info "RAM disponible: ${ram_gb}GB"
    info "Espacio en disco: ${disk_gb}GB"
    
    if [ "$ram_gb" -lt 4 ]; then
        warn "Se recomienda al menos 4GB de RAM. Detectado: ${ram_gb}GB"
    fi
    
    if [ "$disk_gb" -lt 20 ]; then
        error "Se requieren al menos 20GB de espacio libre. Disponible: ${disk_gb}GB"
    fi
}

# Función para crear log de instalación
setup_logging() {
    local log_dir="$HOME/installation-logs"
    mkdir -p "$log_dir"
    
    export INSTALL_LOG="$log_dir/install-$(date +%Y%m%d_%H%M%S).log"
    
    info "Log de instalación: $INSTALL_LOG"
    
    # Redirigir salida a log manteniendo salida en pantalla
    exec > >(tee -a "$INSTALL_LOG")
    exec 2>&1
}

# Función para ejecutar script con manejo de errores
run_script() {
    local script_name=$1
    local description=$2
    
    if [ ! -f "$script_name" ]; then
        error "Script no encontrado: $script_name"
    fi
    
    if [ ! -x "$script_name" ]; then
        chmod +x "$script_name"
    fi
    
    log "Ejecutando: $description"
    
    if ./"$script_name"; then
        success "$description completado exitosamente"
    else
        error "Falló la ejecución de: $description"
    fi
}

# Función para verificar instalación
verify_installation() {
    local errors=0
    
    info "Verificando instalación..."
    
    # Verificar R
    if command -v R &> /dev/null; then
        success "R instalado: $(R --version | head -n1 | cut -d' ' -f3)"
    else
        error "R no está instalado"
        ((errors++))
    fi
    
    # Verificar RStudio
    if command -v rstudio &> /dev/null; then
        success "RStudio instalado"
    else
        warn "RStudio no encontrado en PATH"
    fi
    
    # Verificar LaTeX
    if command -v pdflatex &> /dev/null; then
        success "LaTeX instalado"
    else
        error "LaTeX no está instalado"
        ((errors++))
    fi
    
    # Verificar Python
    if command -v python3 &> /dev/null; then
        success "Python instalado: $(python3 --version)"
    else
        error "Python no está instalado"
        ((errors++))
    fi
    
    # Verificar entorno virtual Python
    if [ -d "$HOME/Python-Envs/rexams-icfes" ]; then
        success "Entorno virtual Python creado"
    else
        warn "Entorno virtual Python no encontrado"
    fi
    
    # Verificar paquete r-exams
    if R -e "library(exams)" &> /dev/null; then
        success "Paquete r-exams instalado"
    else
        error "Paquete r-exams no está instalado"
        ((errors++))
    fi
    
    return $errors
}

# Función principal
main() {
    echo -e "${CYAN}"
    cat << 'EOF'
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                                                                              ║
    ║        INSTALACIÓN COMPLETA VM UBUNTU R-EXAMS ICFES                         ║
    ║                                                                              ║
    ║        🚀 Configuración automática del entorno completo                     ║
    ║        📊 R + RStudio + LaTeX + Python científico                           ║
    ║        🔧 Scripts de automatización y plantillas                            ║
    ║        💾 Sistema de backup y monitoreo                                     ║
    ║                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    
    info "Iniciando instalación completa..."
    
    # Verificaciones previas
    check_ubuntu
    check_system_resources
    setup_logging
    
    # Confirmar instalación
    echo ""
    warn "Esta instalación tomará aproximadamente 30-60 minutos"
    warn "Se descargarán varios GB de paquetes"
    warn "Se requiere conexión a internet estable"
    echo ""
    read -p "¿Continuar con la instalación completa? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "Instalación cancelada por el usuario"
        exit 0
    fi
    
    # Secuencia de instalación
    local total_steps=8
    local current_step=0
    
    # Paso 1: Instalación base
    ((current_step++))
    show_progress $current_step $total_steps "Instalación del entorno base"
    run_script "install-icfes-rexams-environment.sh" "Instalación del entorno base"
    
    # Paso 2: Configuración R
    ((current_step++))
    show_progress $current_step $total_steps "Configuración de R y paquetes"
    run_script "configure-r-environment.sh" "Configuración de R y paquetes"
    
    # Paso 3: Plantillas
    ((current_step++))
    show_progress $current_step $total_steps "Creación de plantillas R-Exams"
    run_script "create-rexams-templates.sh" "Creación de plantillas R-Exams"
    
    # Paso 4: LaTeX
    ((current_step++))
    show_progress $current_step $total_steps "Optimización de LaTeX"
    run_script "optimize-latex-config.sh" "Optimización de LaTeX"
    
    # Paso 5: Python
    ((current_step++))
    show_progress $current_step $total_steps "Configuración de Python científico"
    run_script "setup-python-scientific.sh" "Configuración de Python científico"
    
    # Paso 6: Optimizaciones finales
    ((current_step++))
    show_progress $current_step $total_steps "Aplicación de optimizaciones finales"
    run_script "final-optimizations.sh" "Aplicación de optimizaciones finales"
    
    # Paso 7: Verificación
    ((current_step++))
    show_progress $current_step $total_steps "Verificación de la instalación"
    if verify_installation; then
        success "Verificación completada sin errores"
    else
        warn "Se encontraron algunos problemas en la verificación"
    fi
    
    # Paso 8: Finalización
    ((current_step++))
    show_progress $current_step $total_steps "Finalización y limpieza"
    
    # Limpiar archivos temporales
    sudo apt autoremove -y
    sudo apt autoclean
    
    # Actualizar base de datos de archivos
    sudo updatedb 2>/dev/null || true
    
    success "Limpieza completada"
    
    # Mensaje final
    echo ""
    echo -e "${CYAN}============================================================================${NC}"
    echo -e "${CYAN}                    ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!${NC}"
    echo -e "${CYAN}============================================================================${NC}"
    echo ""
    echo -e "${GREEN}✅ Entorno R-Exams ICFES completamente configurado${NC}"
    echo -e "${GREEN}✅ Todos los componentes instalados y verificados${NC}"
    echo -e "${GREEN}✅ Scripts de automatización disponibles${NC}"
    echo -e "${GREEN}✅ Plantillas y ejemplos creados${NC}"
    echo ""
    echo -e "${BLUE}📋 PRÓXIMOS PASOS:${NC}"
    echo "1. Reiniciar el sistema: sudo reboot"
    echo "2. Ejecutar menú principal: rexams-menu"
    echo "3. Crear primer ejercicio: new_rexams mi_ejercicio"
    echo "4. Leer documentación: README-VM-Ubuntu-RExams-ICFES.md"
    echo ""
    echo -e "${BLUE}📁 ARCHIVOS IMPORTANTES:${NC}"
    echo "  📖 README-VM-Ubuntu-RExams-ICFES.md - Documentación completa"
    echo "  📊 $INSTALL_LOG - Log de instalación"
    echo "  🎛️  rexams-menu - Menú principal de herramientas"
    echo ""
    echo -e "${YELLOW}🔄 SE RECOMIENDA REINICIAR EL SISTEMA AHORA${NC}"
    echo ""
    
    # Preguntar si reiniciar
    read -p "¿Reiniciar el sistema ahora? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        info "Reiniciando sistema en 5 segundos..."
        sleep 5
        sudo reboot
    else
        info "Recuerda reiniciar el sistema antes de usar el entorno"
    fi
}

# Verificar que se ejecuta como usuario normal (no root)
if [ "$EUID" -eq 0 ]; then
    error "No ejecutes este script como root. Usa tu usuario normal."
fi

# Ejecutar función principal
main "$@"
