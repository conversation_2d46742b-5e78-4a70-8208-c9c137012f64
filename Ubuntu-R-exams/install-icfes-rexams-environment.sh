#!/bin/bash

# ============================================================================
# Script de Instalación Automática - RepositorioMatematicasICFES_R_Exams
# Ubuntu 22.04 LTS - Entorno Completo para R-Exams
# ============================================================================

set -e  # Salir si hay errores

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Verificar que se ejecuta en Ubuntu
if ! grep -q "Ubuntu" /etc/os-release; then
    error "Este script está diseñado para Ubuntu 22.04 LTS"
fi

log "🚀 Iniciando instalación del entorno RepositorioMatematicasICFES_R_Exams"

# ============================================================================
# FASE 1: ACTUALIZACIÓN DEL SISTEMA
# ============================================================================

log "📦 Fase 1: Actualizando sistema base..."

sudo apt update
sudo apt upgrade -y

# Instalar herramientas básicas
sudo apt install -y \
    curl wget git vim nano htop tree \
    build-essential software-properties-common \
    apt-transport-https ca-certificates gnupg \
    lsb-release unzip gdebi-core

log "✅ Sistema base actualizado"

# ============================================================================
# FASE 2: CONFIGURACIÓN DE IDIOMA Y REGIÓN
# ============================================================================

log "🌍 Fase 2: Configurando idioma español..."

sudo apt install -y language-pack-es language-pack-es-base
sudo locale-gen es_ES.UTF-8
sudo update-locale LANG=es_ES.UTF-8

# Configurar timezone
sudo timedatectl set-timezone America/Bogota

log "✅ Configuración regional completada"

# ============================================================================
# FASE 3: INSTALACIÓN DE R Y RSTUDIO
# ============================================================================

log "📊 Fase 3: Instalando R y RStudio..."

# Agregar repositorio CRAN
wget -qO- https://cloud.r-project.org/bin/linux/ubuntu/marutter_pubkey.asc | sudo tee -a /etc/apt/trusted.gpg.d/cran_ubuntu_key.asc
echo "deb https://cloud.r-project.org/bin/linux/ubuntu jammy-cran40/" | sudo tee -a /etc/apt/sources.list.d/cran-r.list

sudo apt update

# Instalar R y dependencias
sudo apt install -y \
    r-base r-base-core r-recommended r-base-dev \
    r-cran-devtools r-cran-tidyverse \
    libcurl4-openssl-dev libssl-dev libxml2-dev \
    libfontconfig1-dev libharfbuzz-dev libfribidi-dev \
    libfreetype6-dev libpng-dev libtiff5-dev libjpeg-dev

# Descargar e instalar RStudio Desktop
RSTUDIO_VERSION="2023.12.1-402"
wget https://download1.rstudio.org/electron/jammy/amd64/rstudio-${RSTUDIO_VERSION}-amd64.deb
sudo gdebi -n rstudio-${RSTUDIO_VERSION}-amd64.deb
rm rstudio-${RSTUDIO_VERSION}-amd64.deb

log "✅ R y RStudio instalados"

# ============================================================================
# FASE 4: INSTALACIÓN DE LATEX COMPLETO
# ============================================================================

log "📝 Fase 4: Instalando LaTeX completo..."

# Instalar TeX Live completo
sudo apt install -y \
    texlive-full \
    texlive-lang-spanish \
    texlive-latex-extra \
    texlive-math-extra \
    texlive-pictures \
    texlive-science \
    texlive-fonts-extra \
    texlive-bibtex-extra

# Instalar herramientas adicionales de LaTeX
sudo apt install -y \
    latexmk \
    texstudio \
    lyx

log "✅ LaTeX completo instalado"

# ============================================================================
# FASE 5: INSTALACIÓN DE PYTHON CIENTÍFICO
# ============================================================================

log "🐍 Fase 5: Instalando Python científico..."

# Python y pip
sudo apt install -y \
    python3 python3-pip python3-venv python3-dev \
    python3-setuptools python3-wheel

# Bibliotecas científicas principales
pip3 install --user \
    matplotlib numpy pandas seaborn scipy \
    sympy jupyter notebook ipython \
    plotly bokeh altair

# Bibliotecas adicionales para matemáticas
pip3 install --user \
    networkx scikit-learn statsmodels \
    pillow imageio

log "✅ Python científico instalado"

# ============================================================================
# FASE 6: INSTALACIÓN DE HERRAMIENTAS ADICIONALES
# ============================================================================

log "🛠️ Fase 6: Instalando herramientas adicionales..."

# Pandoc para conversiones de documentos
sudo apt install -y pandoc pandoc-citeproc

# Git LFS para archivos grandes
sudo apt install -y git-lfs
git lfs install

# Visual Studio Code
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" | sudo tee /etc/apt/sources.list.d/vscode.list

sudo apt update
sudo apt install -y code

# Herramientas matemáticas opcionales
sudo apt install -y \
    maxima wxmaxima \
    octave octave-doc

# GeoGebra (via Snap)
if command -v snap &> /dev/null; then
    sudo snap install geogebra
fi

log "✅ Herramientas adicionales instaladas"

# ============================================================================
# FASE 7: CONFIGURACIÓN DE VMWARE TOOLS
# ============================================================================

log "⚙️ Fase 7: Configurando VMware Tools..."

sudo apt install -y \
    open-vm-tools open-vm-tools-desktop \
    open-vm-tools-dev

sudo systemctl enable vmtoolsd
sudo systemctl start vmtoolsd

# Configurar carpetas compartidas
sudo mkdir -p /mnt/hgfs
echo ".host:/ /mnt/hgfs fuse.vmhgfs-fuse allow_other 0 0" | sudo tee -a /etc/fstab

log "✅ VMware Tools configurado"

# ============================================================================
# FASE 8: CREAR ESTRUCTURA DE DIRECTORIOS
# ============================================================================

log "📁 Fase 8: Creando estructura de directorios..."

mkdir -p ~/Proyectos/{RepositorioMatematicasICFES_R_Exams,Plantillas,Ejemplos}
mkdir -p ~/Recursos/{LaTeX-Templates,R-Libraries,Python-Scripts}
mkdir -p ~/Documentos/{Manuales,Referencias}
mkdir -p ~/Backup/{Proyectos,Configuraciones}
mkdir -p ~/Scripts

log "✅ Estructura de directorios creada"

# ============================================================================
# INFORMACIÓN FINAL
# ============================================================================

log "🎉 ¡Instalación completada exitosamente!"
echo ""
echo -e "${BLUE}============================================================================${NC}"
echo -e "${BLUE}  ENTORNO REPOSITORIO MATEMÁTICAS ICFES R-EXAMS CONFIGURADO${NC}"
echo -e "${BLUE}============================================================================${NC}"
echo ""
echo -e "${GREEN}✅ Sistema base actualizado${NC}"
echo -e "${GREEN}✅ R $(R --version | head -n1 | cut -d' ' -f3) y RStudio instalados${NC}"
echo -e "${GREEN}✅ LaTeX completo con soporte español${NC}"
echo -e "${GREEN}✅ Python $(python3 --version | cut -d' ' -f2) científico${NC}"
echo -e "${GREEN}✅ VMware Tools configurado${NC}"
echo -e "${GREEN}✅ Estructura de directorios creada${NC}"
echo ""
echo -e "${YELLOW}📋 PRÓXIMOS PASOS:${NC}"
echo "1. Reiniciar el sistema: sudo reboot"
echo "2. Ejecutar el script de configuración de R: ~/Scripts/configure-r-environment.sh"
echo "3. Configurar Git: git config --global user.name 'Tu Nombre'"
echo "4. Configurar Git: git config --global user.email '<EMAIL>'"
echo ""
echo -e "${BLUE}📁 Directorios principales:${NC}"
echo "   ~/Proyectos/RepositorioMatematicasICFES_R_Exams/"
echo "   ~/Recursos/"
echo "   ~/Scripts/"
echo ""
echo -e "${BLUE}🚀 Para continuar con la configuración específica de R-Exams,${NC}"
echo -e "${BLUE}   ejecuta: bash ~/Scripts/configure-r-environment.sh${NC}"
echo ""

log "🔄 Se recomienda reiniciar el sistema ahora"

# Hacer el script ejecutable
chmod +x ~/Scripts/configure-r-environment.sh 2>/dev/null || true
