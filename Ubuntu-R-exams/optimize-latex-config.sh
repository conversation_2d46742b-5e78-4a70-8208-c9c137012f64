#!/bin/bash

# ============================================================================
# Script de Optimización LaTeX para R-Exams ICFES
# Configuración avanzada de LaTeX con paquetes matemáticos y TikZ
# ============================================================================

set -e

GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

log "📝 Optimizando configuración LaTeX para R-Exams ICFES..."

# ============================================================================
# VERIFICAR INSTALACIÓN DE LATEX
# ============================================================================

log "🔍 Verificando instalación de LaTeX..."

if ! command -v pdflatex &> /dev/null; then
    warn "LaTeX no está instalado. Instalando TeX Live completo..."
    sudo apt update
    sudo apt install -y texlive-full texlive-lang-spanish
fi

# Verificar paquetes específicos
latex_packages=(
    "texlive-latex-extra"
    "texlive-math-extra" 
    "texlive-pictures"
    "texlive-science"
    "texlive-fonts-extra"
    "texlive-bibtex-extra"
    "latexmk"
)

for package in "${latex_packages[@]}"; do
    if ! dpkg -l | grep -q "^ii  $package "; then
        log "📦 Instalando $package..."
        sudo apt install -y "$package"
    fi
done

log "✅ LaTeX verificado e instalado"

# ============================================================================
# CREAR CONFIGURACIÓN PERSONALIZADA DE LATEX
# ============================================================================

log "⚙️ Creando configuración personalizada de LaTeX..."

# Crear directorio personal de LaTeX
mkdir -p ~/texmf/tex/latex/local

# Crear archivo de configuración personalizada para r-exams
cat > ~/texmf/tex/latex/local/rexams-icfes.sty << 'EOF'
% ============================================================================
% Paquete personalizado para R-Exams ICFES
% Configuración optimizada para exámenes matemáticos
% ============================================================================

\NeedsTeXFormat{LaTeX2e}
\ProvidesPackage{rexams-icfes}[2024/01/01 Configuracion R-Exams ICFES]

% Paquetes básicos
\RequirePackage[utf8]{inputenc}
\RequirePackage[T1]{fontenc}
\RequirePackage[spanish,es-tabla]{babel}
\RequirePackage{lmodern}

% Matemáticas
\RequirePackage{amsmath}
\RequirePackage{amsfonts}
\RequirePackage{amssymb}
\RequirePackage{amsthm}
\RequirePackage{mathtools}
\RequirePackage{cancel}
\RequirePackage{xfrac}

% Gráficos y figuras
\RequirePackage{graphicx}
\RequirePackage{float}
\RequirePackage{tikz}
\RequirePackage{pgfplots}
\RequirePackage{pgfplotstable}

% Configuración de TikZ
\usetikzlibrary{
    arrows,
    arrows.meta,
    calc,
    decorations.markings,
    decorations.pathreplacing,
    patterns,
    positioning,
    shapes.geometric,
    angles,
    quotes
}

% Configuración de PGFPlots
\pgfplotsset{compat=1.18}
\pgfplotsset{
    every axis/.append style={
        font=\small,
        line width=0.8pt,
        tick style={line width=0.6pt}
    }
}

% Tablas
\RequirePackage{array}
\RequirePackage{booktabs}
\RequirePackage{colortbl}
\RequirePackage{multirow}
\RequirePackage{longtable}

% Colores
\RequirePackage{xcolor}
\definecolor{icfesblue}{RGB}{0,102,153}
\definecolor{icfesgreen}{RGB}{0,153,76}
\definecolor{icfesred}{RGB}{204,0,51}
\definecolor{icfesorange}{RGB}{255,102,0}

% Configuración de página
\RequirePackage{geometry}
\geometry{
    a4paper,
    margin=2cm,
    top=2.5cm,
    bottom=2.5cm
}

% Encabezados y pies de página
\RequirePackage{fancyhdr}
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{\small\textbf{ICFES - Matemáticas}}
\fancyhead[R]{\small\thepage}
\renewcommand{\headrulewidth}{0.4pt}

% Comandos personalizados para matemáticas
\newcommand{\fraccion}[2]{\frac{#1}{#2}}
\newcommand{\raiz}[2][]{\sqrt[#1]{#2}}
\newcommand{\potencia}[2]{#1^{#2}}
\newcommand{\logaritmo}[2][]{\log_{#1}{#2}}

% Entornos para ejercicios
\newtheorem{ejercicio}{Ejercicio}
\newtheorem{ejemplo}{Ejemplo}
\newtheorem{definicion}{Definición}

% Configuración de listas
\RequirePackage{enumitem}
\setlist[enumerate]{label=\alph*)}
\setlist[itemize]{label=\textbullet}

% Espaciado
\RequirePackage{setspace}
\onehalfspacing

% Configuración de código (si se necesita)
\RequirePackage{listings}
\lstset{
    basicstyle=\ttfamily\small,
    breaklines=true,
    frame=single,
    backgroundcolor=\color{gray!10}
}

\endinput
EOF

# Actualizar base de datos de LaTeX
texhash ~/texmf

log "✅ Configuración personalizada de LaTeX creada"

# ============================================================================
# CREAR PLANTILLAS LATEX ESPECÍFICAS
# ============================================================================

log "📄 Creando plantillas LaTeX específicas..."

# Plantilla para exámenes
cat > ~/Recursos/LaTeX-Templates/examen-icfes.tex << 'EOF'
\documentclass[12pt,a4paper]{article}
\usepackage{rexams-icfes}

\title{Examen de Matemáticas ICFES}
\author{Repositorio Matemáticas ICFES}
\date{\today}

\begin{document}

\maketitle

\section{Instrucciones}
\begin{itemize}
    \item Lea cuidadosamente cada pregunta antes de responder.
    \item Marque solo una opción por pregunta.
    \item Use lápiz No. 2 para marcar sus respuestas.
    \item Tiempo límite: 90 minutos.
\end{itemize}

\section{Preguntas}

% Aquí van las preguntas generadas por r-exams

\end{document}
EOF

# Plantilla para ejercicios individuales
cat > ~/Recursos/LaTeX-Templates/ejercicio-individual.tex << 'EOF'
\documentclass[12pt,a4paper]{article}
\usepackage{rexams-icfes}

\begin{document}

\begin{ejercicio}
% Contenido del ejercicio aquí
\end{ejercicio}

\textbf{Opciones:}
\begin{enumerate}
    \item Opción A
    \item Opción B
    \item Opción C
    \item Opción D
\end{enumerate}

\textbf{Solución:}
% Solución detallada aquí

\end{document}
EOF

log "✅ Plantillas LaTeX creadas"

# ============================================================================
# CONFIGURAR TIKZ PARA R-EXAMS
# ============================================================================

log "🎨 Configurando TikZ para R-Exams..."

# Crear configuración específica de TikZ para R
cat > ~/R/tikz-config.R << 'EOF'
# ============================================================================
# Configuración TikZ para R-Exams ICFES
# ============================================================================

# Configuración global de TikZ
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)

# Paquetes LaTeX para TikZ
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{pgfplots}",
  "\\usepackage{amsmath}",
  "\\usepackage{amsfonts}",
  "\\usepackage{amssymb}",
  "\\usepackage[utf8]{inputenc}",
  "\\usepackage[T1]{fontenc}",
  "\\usepackage[spanish]{babel}",
  "\\usepackage{xcolor}",
  "\\usetikzlibrary{arrows,arrows.meta,calc,patterns,positioning,shapes.geometric,angles,quotes}",
  "\\pgfplotsset{compat=1.18}"
))

# Configuración de dispositivo TikZ
options(tikzDefaultEngine = "pdftex")
options(tikzMetricPackages = c("\\usepackage[utf8]{inputenc}",
                              "\\usepackage[T1]{fontenc}",
                              "\\usetikzlibrary{calc}"))

# Función para configurar TikZ en chunks de R
setup_tikz <- function() {
  library(tikzDevice)
  
  # Configurar opciones específicas
  options(tikzDocumentDeclaration = "\\documentclass[12pt]{article}")
  
  # Configurar directorio de trabajo para TikZ
  tikz_dir <- file.path(getwd(), "tikz-figures")
  if (!dir.exists(tikz_dir)) {
    dir.create(tikz_dir, recursive = TRUE)
  }
  
  cat("✅ TikZ configurado para R-Exams\n")
  cat("📁 Directorio TikZ:", tikz_dir, "\n")
}

# Función para limpiar archivos temporales de TikZ
clean_tikz <- function() {
  tikz_files <- list.files(pattern = "\\.(fls|fdb_latexmk|aux|log|figpath|makefile|figpath)$")
  if (length(tikz_files) > 0) {
    file.remove(tikz_files)
    cat("🧹 Archivos temporales TikZ eliminados:", length(tikz_files), "\n")
  }
}

cat("📝 Configuración TikZ cargada\n")
cat("🔧 Usar: setup_tikz() para inicializar\n")
cat("🧹 Usar: clean_tikz() para limpiar archivos temporales\n")
EOF

log "✅ Configuración TikZ para R creada"

# ============================================================================
# CREAR SCRIPT DE PRUEBA LATEX
# ============================================================================

log "🧪 Creando script de prueba LaTeX..."

cat > ~/Scripts/test-latex.sh << 'EOF'
#!/bin/bash

# Script para probar la configuración LaTeX

echo "🧪 Probando configuración LaTeX..."

# Crear documento de prueba
cat > /tmp/test-latex.tex << 'TESTDOC'
\documentclass[12pt,a4paper]{article}
\usepackage{rexams-icfes}

\begin{document}

\title{Prueba de Configuración LaTeX}
\author{Test}
\date{\today}
\maketitle

\section{Prueba de Matemáticas}

Ecuación simple: $x^2 + y^2 = r^2$

Ecuación compleja:
$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$

\section{Prueba de TikZ}

\begin{tikzpicture}
\draw[->] (0,0) -- (3,0) node[right] {$x$};
\draw[->] (0,0) -- (0,3) node[above] {$y$};
\draw[domain=0:2.5,smooth,variable=\x,blue,thick] plot ({\x},{0.5*\x*\x});
\end{tikzpicture}

\section{Prueba de Colores ICFES}

\textcolor{icfesblue}{Texto en azul ICFES}

\textcolor{icfesgreen}{Texto en verde ICFES}

\end{document}
TESTDOC

# Compilar documento de prueba
cd /tmp
if pdflatex test-latex.tex > /dev/null 2>&1; then
    echo "✅ LaTeX funciona correctamente"
    echo "📄 PDF de prueba generado: /tmp/test-latex.pdf"
    
    # Verificar que el PDF se creó
    if [ -f "test-latex.pdf" ]; then
        echo "✅ PDF generado exitosamente"
    else
        echo "❌ Error: PDF no se generó"
    fi
else
    echo "❌ Error en la compilación LaTeX"
    echo "📋 Revisa los logs en /tmp/test-latex.log"
fi

# Limpiar archivos temporales
rm -f test-latex.aux test-latex.log test-latex.tex

echo "🎉 Prueba de LaTeX completada"
EOF

chmod +x ~/Scripts/test-latex.sh

log "✅ Script de prueba LaTeX creado"

# ============================================================================
# INFORMACIÓN FINAL
# ============================================================================

echo ""
echo -e "${BLUE}============================================================================${NC}"
echo -e "${BLUE}  CONFIGURACIÓN LATEX OPTIMIZADA${NC}"
echo -e "${BLUE}============================================================================${NC}"
echo ""
echo -e "${GREEN}✅ Paquete personalizado: rexams-icfes.sty${NC}"
echo -e "${GREEN}✅ Plantillas LaTeX creadas${NC}"
echo -e "${GREEN}✅ Configuración TikZ optimizada${NC}"
echo -e "${GREEN}✅ Script de prueba disponible${NC}"
echo ""
echo -e "${BLUE}🧪 Para probar la configuración:${NC}"
echo "  bash ~/Scripts/test-latex.sh"
echo ""
echo -e "${BLUE}📁 Archivos creados:${NC}"
echo "  ~/texmf/tex/latex/local/rexams-icfes.sty"
echo "  ~/Recursos/LaTeX-Templates/"
echo "  ~/R/tikz-config.R"
echo "  ~/Scripts/test-latex.sh"
echo ""

log "🎉 ¡Configuración LaTeX optimizada completada!"
