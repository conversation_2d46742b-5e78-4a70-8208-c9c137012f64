#!/bin/bash

# ============================================================================
# Script de Configuración Python Científico para R-Exams ICFES
# Instalación y configuración de Python con integración R via reticulate
# ============================================================================

set -e

GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

log "🐍 Configurando Python científico para R-Exams ICFES..."

# ============================================================================
# VERIFICAR E INSTALAR PYTHON
# ============================================================================

log "🔍 Verificando instalación de Python..."

# Verificar Python 3
if ! command -v python3 &> /dev/null; then
    log "📦 Instalando Python 3..."
    sudo apt update
    sudo apt install -y python3 python3-pip python3-venv python3-dev
fi

# Verificar pip
if ! command -v pip3 &> /dev/null; then
    log "📦 Instalando pip..."
    sudo apt install -y python3-pip
fi

# Actualizar pip
python3 -m pip install --user --upgrade pip

log "✅ Python $(python3 --version) verificado"

# ============================================================================
# CREAR ENTORNO VIRTUAL PARA R-EXAMS
# ============================================================================

log "🏗️ Creando entorno virtual para R-Exams..."

# Crear directorio para entornos virtuales
mkdir -p ~/Python-Envs

# Crear entorno virtual específico para r-exams
python3 -m venv ~/Python-Envs/rexams-icfes

# Activar entorno virtual
source ~/Python-Envs/rexams-icfes/bin/activate

# Actualizar pip en el entorno virtual
pip install --upgrade pip setuptools wheel

log "✅ Entorno virtual 'rexams-icfes' creado"

# ============================================================================
# INSTALAR PAQUETES CIENTÍFICOS ESENCIALES
# ============================================================================

log "📊 Instalando paquetes científicos esenciales..."

# Paquetes científicos fundamentales
scientific_packages=(
    "numpy>=1.21.0"
    "pandas>=1.3.0"
    "matplotlib>=3.5.0"
    "seaborn>=0.11.0"
    "scipy>=1.7.0"
    "sympy>=1.9.0"
    "plotly>=5.0.0"
    "bokeh>=2.4.0"
    "altair>=4.2.0"
)

for package in "${scientific_packages[@]}"; do
    log "📦 Instalando $package..."
    pip install "$package"
done

log "✅ Paquetes científicos esenciales instalados"

# ============================================================================
# INSTALAR PAQUETES MATEMÁTICOS ESPECIALIZADOS
# ============================================================================

log "🔢 Instalando paquetes matemáticos especializados..."

# Paquetes matemáticos adicionales
math_packages=(
    "networkx>=2.6"
    "scikit-learn>=1.0.0"
    "statsmodels>=0.13.0"
    "pillow>=8.3.0"
    "imageio>=2.9.0"
    "mpmath>=1.2.0"
    "fractions"
    "decimal"
)

for package in "${math_packages[@]}"; do
    log "📦 Instalando $package..."
    pip install "$package"
done

# Paquetes para gráficos avanzados
graphics_packages=(
    "plotnine>=0.8.0"
    "pygraphviz"
    "graphviz"
    "pydot"
    "matplotlib-venn"
)

# Instalar paquetes gráficos (algunos pueden fallar por dependencias del sistema)
for package in "${graphics_packages[@]}"; do
    log "📦 Intentando instalar $package..."
    pip install "$package" || warn "No se pudo instalar $package (dependencias del sistema requeridas)"
done

log "✅ Paquetes matemáticos especializados instalados"

# ============================================================================
# INSTALAR JUPYTER Y HERRAMIENTAS DE DESARROLLO
# ============================================================================

log "📓 Instalando Jupyter y herramientas de desarrollo..."

dev_packages=(
    "jupyter>=1.0.0"
    "notebook>=6.4.0"
    "ipython>=7.25.0"
    "jupyterlab>=3.1.0"
    "ipywidgets>=7.6.0"
    "black>=21.0.0"
    "flake8>=3.9.0"
    "pytest>=6.2.0"
)

for package in "${dev_packages[@]}"; do
    log "📦 Instalando $package..."
    pip install "$package"
done

log "✅ Herramientas de desarrollo instaladas"

# Desactivar entorno virtual
deactivate

# ============================================================================
# CONFIGURAR INTEGRACIÓN CON R (RETICULATE)
# ============================================================================

log "🔗 Configurando integración Python-R..."

# Crear script de configuración para reticulate
cat > ~/R/python-config.R << 'EOF'
# ============================================================================
# Configuración Python para R-Exams ICFES
# Integración Python-R via reticulate
# ============================================================================

library(reticulate)

# Configurar entorno virtual de Python
python_env <- "~/Python-Envs/rexams-icfes"
use_virtualenv(python_env, required = TRUE)

# Verificar configuración
cat("🐍 Configuración Python para R-Exams:\n")
cat("📁 Entorno virtual:", python_env, "\n")
cat("🔧 Python ejecutable:", py_config()$python, "\n")
cat("📦 Versión Python:", py_config()$version, "\n")

# Función para verificar paquetes Python
check_python_packages <- function() {
  required_packages <- c(
    "numpy", "pandas", "matplotlib", "seaborn", 
    "scipy", "sympy", "plotly", "bokeh"
  )
  
  cat("📋 Verificando paquetes Python:\n")
  for (pkg in required_packages) {
    if (py_module_available(pkg)) {
      cat("✅", pkg, "\n")
    } else {
      cat("❌", pkg, "(no disponible)\n")
    }
  }
}

# Función para importar módulos comunes
import_python_modules <- function() {
  # Importar módulos científicos comunes
  np <<- import("numpy")
  pd <<- import("pandas") 
  plt <<- import("matplotlib.pyplot")
  sns <<- import("seaborn")
  sp <<- import("scipy")
  sym <<- import("sympy")
  
  cat("📦 Módulos Python importados:\n")
  cat("  - numpy como 'np'\n")
  cat("  - pandas como 'pd'\n")
  cat("  - matplotlib.pyplot como 'plt'\n")
  cat("  - seaborn como 'sns'\n")
  cat("  - scipy como 'sp'\n")
  cat("  - sympy como 'sym'\n")
}

# Función para configurar matplotlib para r-exams
setup_matplotlib <- function() {
  py_run_string("
import matplotlib
matplotlib.use('Agg')  # Backend no interactivo
import matplotlib.pyplot as plt
import numpy as np

# Configuración global de matplotlib
plt.rcParams['figure.figsize'] = (8, 6)
plt.rcParams['figure.dpi'] = 150
plt.rcParams['font.size'] = 12
plt.rcParams['axes.labelsize'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['legend.fontsize'] = 10
plt.rcParams['font.family'] = 'serif'
plt.rcParams['text.usetex'] = False  # Desactivar LaTeX en matplotlib por defecto
")
  
  cat("🎨 Matplotlib configurado para r-exams\n")
}

# Función de ejemplo para crear gráfico con Python
ejemplo_grafico_python <- function() {
  py_run_string("
import matplotlib.pyplot as plt
import numpy as np

# Datos de ejemplo
x = np.linspace(0, 2*np.pi, 100)
y = np.sin(x)

# Crear gráfico
plt.figure(figsize=(8, 5))
plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
plt.xlabel('x')
plt.ylabel('y')
plt.title('Función Seno')
plt.grid(True, alpha=0.3)
plt.legend()
plt.tight_layout()

# Guardar gráfico
plt.savefig('ejemplo_python.png', dpi=150, bbox_inches='tight')
plt.close()
")
  
  cat("📊 Gráfico de ejemplo creado: ejemplo_python.png\n")
}

cat("🔧 Configuración Python-R cargada\n")
cat("📋 Usar: check_python_packages() para verificar paquetes\n")
cat("📦 Usar: import_python_modules() para importar módulos\n")
cat("🎨 Usar: setup_matplotlib() para configurar gráficos\n")
cat("📊 Usar: ejemplo_grafico_python() para crear gráfico de prueba\n")
EOF

log "✅ Configuración Python-R creada"

# ============================================================================
# CREAR EJEMPLOS DE USO
# ============================================================================

log "📝 Creando ejemplos de uso Python en R-Exams..."

# Crear directorio para ejemplos
mkdir -p ~/Proyectos/RepositorioMatematicasICFES_R_Exams/Ejemplos/Python

# Ejemplo 1: Gráfico matemático con matplotlib
cat > ~/Proyectos/RepositorioMatematicasICFES_R_Exams/Ejemplos/Python/grafico_funcion_cuadratica.Rmd << 'EOF'
---
output:
  pdf_document:
    keep_tex: true
  html_document: default
---

```{r setup, include=FALSE}
library(exams)
library(reticulate)
source("~/R/python-config.R")
setup_matplotlib()
```

```{r variables, echo=FALSE, results='hide'}
# Parámetros de la función cuadrática
a <- sample(c(-3:-1, 1:3), 1)
b <- sample(-5:5, 1)
c <- sample(-10:10, 1)

# Calcular vértice
x_vertice <- -b / (2 * a)
y_vertice <- a * x_vertice^2 + b * x_vertice + c
```

```{r generar_grafico, echo=FALSE, results='hide'}
# Código Python para generar gráfico
py_run_string(sprintf("
import matplotlib.pyplot as plt
import numpy as np

# Parámetros de la función
a, b, c = %d, %d, %d

# Generar datos
x = np.linspace(-10, 10, 400)
y = a * x**2 + b * x + c

# Crear gráfico
plt.figure(figsize=(8, 6))
plt.plot(x, y, 'b-', linewidth=2, label=f'$y = {a}x^2 + {b}x + {c}$')

# Marcar vértice
x_vertice = -b / (2 * a)
y_vertice = a * x_vertice**2 + b * x_vertice + c
plt.plot(x_vertice, y_vertice, 'ro', markersize=8, label=f'Vértice ({x_vertice:.1f}, {y_vertice:.1f})')

# Configuración del gráfico
plt.xlabel('x', fontsize=12)
plt.ylabel('y', fontsize=12)
plt.title('Función Cuadrática', fontsize=14, fontweight='bold')
plt.grid(True, alpha=0.3)
plt.axhline(y=0, color='k', linewidth=0.5)
plt.axvline(x=0, color='k', linewidth=0.5)
plt.legend()
plt.tight_layout()

# Guardar gráfico
plt.savefig('funcion_cuadratica.png', dpi=150, bbox_inches='tight')
plt.close()
", a, b, c))
```

Question
========

Observa el gráfico de la función cuadrática:

![](funcion_cuadratica.png){width=70%}

¿Cuáles son las coordenadas del vértice de la parábola $y = `r a`x^2 + `r b`x + `r c`$?

Answerlist
----------
- $(`r round(x_vertice, 1)`, `r round(y_vertice, 1)`)$
- $(`r round(x_vertice + 1, 1)`, `r round(y_vertice, 1)`)$
- $(`r round(x_vertice, 1)`, `r round(y_vertice + 2, 1)`)$
- $(`r round(x_vertice - 1, 1)`, `r round(y_vertice - 1, 1)`)$

Solution
========

Para encontrar el vértice de una parábola $y = ax^2 + bx + c$, usamos:

$$x_v = -\frac{b}{2a} = -\frac{`r b`}{2(`r a`)} = `r round(x_vertice, 1)`$$

$$y_v = a(x_v)^2 + b(x_v) + c = `r a`(`r round(x_vertice, 1)`)^2 + `r b`(`r round(x_vertice, 1)`) + `r c` = `r round(y_vertice, 1)`$$

Por lo tanto, el vértice está en $(`r round(x_vertice, 1)`, `r round(y_vertice, 1)`)$.

Answerlist
----------
- Verdadero
- Falso
- Falso
- Falso

Meta-information
================
exname: vertice_parabola_python
extype: schoice
exsolution: 1000
exshuffle: TRUE
EOF

log "✅ Ejemplos de uso creados"

# ============================================================================
# CREAR SCRIPT DE PRUEBA PYTHON
# ============================================================================

log "🧪 Creando script de prueba Python..."

cat > ~/Scripts/test-python-integration.sh << 'EOF'
#!/bin/bash

echo "🧪 Probando integración Python-R..."

# Activar entorno virtual
source ~/Python-Envs/rexams-icfes/bin/activate

# Probar paquetes Python
python3 -c "
import sys
print('🐍 Python version:', sys.version)

packages = ['numpy', 'pandas', 'matplotlib', 'seaborn', 'scipy', 'sympy']
for pkg in packages:
    try:
        __import__(pkg)
        print(f'✅ {pkg}')
    except ImportError:
        print(f'❌ {pkg}')
"

# Desactivar entorno virtual
deactivate

# Probar integración con R
R -e "
source('~/R/python-config.R')
check_python_packages()
cat('🔗 Integración Python-R funcionando\n')
"

echo "🎉 Prueba de integración completada"
EOF

chmod +x ~/Scripts/test-python-integration.sh

log "✅ Script de prueba creado"

# ============================================================================
# INFORMACIÓN FINAL
# ============================================================================

echo ""
echo -e "${BLUE}============================================================================${NC}"
echo -e "${BLUE}  PYTHON CIENTÍFICO CONFIGURADO${NC}"
echo -e "${BLUE}============================================================================${NC}"
echo ""
echo -e "${GREEN}✅ Entorno virtual: ~/Python-Envs/rexams-icfes${NC}"
echo -e "${GREEN}✅ Paquetes científicos instalados${NC}"
echo -e "${GREEN}✅ Integración R-Python configurada${NC}"
echo -e "${GREEN}✅ Ejemplos de uso creados${NC}"
echo ""
echo -e "${BLUE}🧪 Para probar la configuración:${NC}"
echo "  bash ~/Scripts/test-python-integration.sh"
echo ""
echo -e "${BLUE}🐍 Para usar Python en R:${NC}"
echo "  source('~/R/python-config.R')"
echo "  check_python_packages()"
echo "  import_python_modules()"
echo ""
echo -e "${BLUE}📁 Archivos creados:${NC}"
echo "  ~/Python-Envs/rexams-icfes/"
echo "  ~/R/python-config.R"
echo "  ~/Proyectos/.../Ejemplos/Python/"
echo ""

log "🎉 ¡Configuración Python científico completada!"
