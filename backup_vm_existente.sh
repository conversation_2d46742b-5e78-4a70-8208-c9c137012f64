#!/bin/bash

# Script para hacer respaldo de la VM existente antes de modificarla

echo "💾 CREANDO RESPALDO DE TU VM EXISTENTE"
echo "====================================="

VM_DIR="$HOME/.var/app/org.gnome.Boxes/data/gnome-boxes/images"
VM_FILE="manjaro-xfce-2-2"
BACKUP_DIR="$HOME/Backups-VMs"

echo ""
echo "📋 Información de la VM:"
echo "========================"

if [ -f "$VM_DIR/$VM_FILE" ]; then
    echo "✅ VM encontrada: $VM_FILE"
    
    # Mostrar información del archivo
    VM_SIZE=$(du -h "$VM_DIR/$VM_FILE" | cut -f1)
    VM_DATE=$(stat -c %y "$VM_DIR/$VM_FILE" | cut -d' ' -f1)
    
    echo "   Tamaño: $VM_SIZE"
    echo "   Última modificación: $VM_DATE"
    echo "   Ubicación: $VM_DIR"
else
    echo "❌ VM no encontrada en: $VM_DIR/$VM_FILE"
    echo ""
    echo "🔍 VMs disponibles:"
    ls -la "$VM_DIR/" 2>/dev/null || echo "   Directorio no encontrado"
    exit 1
fi

echo ""
echo "💾 Creando respaldo..."
echo "====================="

# Crear directorio de respaldos
mkdir -p "$BACKUP_DIR"
echo "✅ Directorio de respaldos: $BACKUP_DIR"

# Verificar espacio disponible
AVAILABLE_SPACE=$(df -h "$BACKUP_DIR" | tail -1 | awk '{print $4}')
echo "💽 Espacio disponible: $AVAILABLE_SPACE"

# Crear nombre del respaldo con fecha
BACKUP_NAME="${VM_FILE}_backup_$(date +%Y%m%d_%H%M%S)"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"

echo ""
echo "🔄 Copiando VM..."
echo "Origen: $VM_DIR/$VM_FILE"
echo "Destino: $BACKUP_PATH"
echo ""
echo "⏳ Esto puede tardar varios minutos (55GB)..."

# Copiar con progreso
if command -v pv &> /dev/null; then
    # Si pv está disponible, mostrar progreso
    pv "$VM_DIR/$VM_FILE" > "$BACKUP_PATH"
else
    # Copia normal
    cp "$VM_DIR/$VM_FILE" "$BACKUP_PATH"
fi

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ RESPALDO COMPLETADO"
    echo "====================="
    
    # Verificar el respaldo
    BACKUP_SIZE=$(du -h "$BACKUP_PATH" | cut -f1)
    echo "✅ Respaldo creado: $BACKUP_NAME"
    echo "✅ Tamaño: $BACKUP_SIZE"
    echo "✅ Ubicación: $BACKUP_PATH"
    
    # Crear archivo de información
    cat > "$BACKUP_DIR/${BACKUP_NAME}.info" << EOF
RESPALDO DE VM MANJARO XFCE
===========================
Fecha de respaldo: $(date)
VM original: $VM_DIR/$VM_FILE
Tamaño original: $VM_SIZE
Tamaño respaldo: $BACKUP_SIZE
Sistema: SuperManjaro optimizado

PARA RESTAURAR:
cp "$BACKUP_PATH" "$VM_DIR/$VM_FILE"
EOF
    
    echo ""
    echo "📄 Archivo de información creado: ${BACKUP_NAME}.info"
    
else
    echo ""
    echo "❌ ERROR AL CREAR RESPALDO"
    echo "=========================="
    echo "No se pudo completar el respaldo."
    echo "Verifica el espacio disponible y permisos."
    exit 1
fi

echo ""
echo "🎯 PRÓXIMOS PASOS:"
echo "=================="
echo "1. ✅ Respaldo completado - tu VM está segura"
echo "2. 🔄 Ahora puedes modificar los recursos sin riesgo"
echo "3. 🚀 Sigue la guía para aumentar RAM y CPUs"
echo ""
echo "💡 PARA RESTAURAR (si algo sale mal):"
echo "cp \"$BACKUP_PATH\" \"$VM_DIR/$VM_FILE\""
echo ""
echo "📁 Todos los respaldos están en: $BACKUP_DIR"
