#!/bin/bash

# Script de optimización completa para Manjaro con 40GB RAM
# Aplica todas las configuraciones y muestra el estado del sistema

echo "🚀 OPTIMIZACIÓN COMPLETA DE MANJARO PARA 40GB RAM"
echo "=================================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

show_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

show_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Aplicar configuraciones del sistema
show_header "APLICANDO CONFIGURACIONES DEL SISTEMA"

show_info "Aplicando configuraciones de memoria..."
sudo sysctl -p /etc/sysctl.d/99-memory-optimization.conf 2>/dev/null && show_message "Configuraciones de memoria aplicadas"

show_info "Aplicando configuraciones de swappiness..."
sudo sysctl -p /etc/sysctl.d/99-swappiness.conf 2>/dev/null && show_message "Swappiness configurado"

# Verificar ZRAM
show_header "VERIFICANDO ZRAM"
if [ -f /etc/systemd/zram-generator.conf ]; then
    show_message "ZRAM configurado correctamente"
    show_info "Configuración ZRAM: 8GB de RAM para swap comprimido"
else
    show_warning "ZRAM no configurado"
fi

# Mostrar información del sistema
show_header "INFORMACIÓN DEL SISTEMA"

echo -e "${CYAN}Memoria Total:${NC} $(free -h | awk '/^Mem:/ {print $2}')"
echo -e "${CYAN}Memoria Libre:${NC} $(free -h | awk '/^Mem:/ {print $7}')"
echo -e "${CYAN}Memoria Disponible:${NC} $(free -h | awk '/^Mem:/ {print $7}')"
echo -e "${CYAN}Swap Total:${NC} $(free -h | awk '/^Swap:/ {print $2}')"

# Mostrar configuración de swappiness
CURRENT_SWAPPINESS=$(cat /proc/sys/vm/swappiness)
echo -e "${CYAN}Swappiness Actual:${NC} $CURRENT_SWAPPINESS"

# Mostrar información de almacenamiento
show_header "INFORMACIÓN DE ALMACENAMIENTO"
df -h / | tail -1 | awk '{print "Espacio Libre: " $4 " de " $2 " (" $5 " usado)"}'

# Mostrar kernels instalados
show_header "KERNELS INSTALADOS"
mhwd-kernel -li

# Mostrar servicios optimizados
show_header "SERVICIOS OPTIMIZADOS"
if ! systemctl is-enabled cups.service &>/dev/null; then
    show_message "CUPS deshabilitado"
else
    show_warning "CUPS aún habilitado"
fi

if ! systemctl is-enabled ModemManager.service &>/dev/null; then
    show_message "ModemManager deshabilitado"
else
    show_warning "ModemManager aún habilitado"
fi

if systemctl is-enabled irqbalance.service &>/dev/null; then
    show_message "IRQBalance habilitado"
else
    show_warning "IRQBalance no habilitado"
fi

# Mostrar estado del firewall
show_header "ESTADO DEL FIREWALL"
sudo ufw status | head -5

# Mostrar procesos que más memoria consumen
show_header "TOP 5 PROCESOS POR MEMORIA"
ps aux --sort=-%mem | head -6 | tail -5 | awk '{printf "%-15s %6s %6s %s\n", $1, $4"%", $6/1024"MB", $11}'

# Mostrar configuraciones de navegadores
show_header "OPTIMIZACIONES DE NAVEGADORES"
if [ -f ~/.local/bin/brave-optimized ]; then
    show_message "Brave optimizado disponible"
else
    show_warning "Brave optimizado no encontrado"
fi

if [ -f ~/.mozilla/firefox/*/user.js ]; then
    show_message "Firefox optimizado"
else
    show_warning "Firefox no optimizado"
fi

# Mostrar recomendaciones finales
show_header "RECOMENDACIONES FINALES"
show_info "1. Reinicia el sistema para aplicar todas las optimizaciones"
show_info "2. Usa el kernel 6.16 para mejor rendimiento"
show_info "3. Ejecuta este script semanalmente para mantener optimizaciones"
show_info "4. Usa 'brave-optimized' para lanzar Brave con optimizaciones"
show_info "5. Monitorea el uso de memoria con 'htop' o 'free -h'"

echo ""
echo -e "${GREEN}🎉 OPTIMIZACIÓN COMPLETA FINALIZADA${NC}"
echo -e "${BLUE}Tu sistema está configurado para aprovechar al máximo los 40GB de RAM${NC}"
