#!/bin/bash

# Script para conectar directamente a la VM usando diferentes métodos

VM_NAME="Ubuntu-Dev"

echo "🖥️  CONECTANDO DIRECTAMENTE A LA VM"
echo "==================================="

# Configurar libvirt de usuario
export LIBVIRT_DEFAULT_URI="qemu:///session"

echo "1. Verificando estado de la VM..."
VM_STATUS=$(virsh list | grep "$VM_NAME" | awk '{print $3}')
if [ "$VM_STATUS" = "ejecutando" ]; then
    echo "✅ VM está ejecutándose"
else
    echo "⚠️  VM no está ejecutándose, iniciando..."
    virsh start "$VM_NAME"
    sleep 3
fi

echo ""
echo "2. Obteniendo información de display..."
DISPLAY_INFO=$(virsh domdisplay "$VM_NAME" 2>/dev/null)
if [ -n "$DISPLAY_INFO" ]; then
    echo "✅ Display encontrado: $DISPLAY_INFO"
else
    echo "🔍 Buscando puerto VNC manualmente..."
    VNC_PORT=$(ss -tlnp | grep :59 | head -1 | awk '{print $4}' | cut -d: -f2)
    if [ -n "$VNC_PORT" ]; then
        DISPLAY_INFO="vnc://127.0.0.1:$VNC_PORT"
        echo "✅ VNC encontrado en puerto: $VNC_PORT"
    else
        echo "❌ No se pudo encontrar display"
    fi
fi

echo ""
echo "3. Intentando conectar con diferentes clientes..."

# Método 1: virt-viewer (si está disponible)
if command -v virt-viewer &> /dev/null; then
    echo "🚀 Abriendo con virt-viewer..."
    virt-viewer -c qemu:///session "$VM_NAME" &
    echo "✅ virt-viewer iniciado"
elif command -v remote-viewer &> /dev/null; then
    echo "🚀 Abriendo con remote-viewer..."
    remote-viewer "$DISPLAY_INFO" &
    echo "✅ remote-viewer iniciado"
elif command -v vinagre &> /dev/null; then
    echo "🚀 Abriendo con vinagre..."
    vinagre "$DISPLAY_INFO" &
    echo "✅ vinagre iniciado"
else
    echo "⚠️  No se encontró cliente VNC instalado"
    echo ""
    echo "📋 OPCIONES PARA INSTALAR CLIENTE VNC:"
    echo "1. virt-viewer: sudo pacman -S virt-viewer"
    echo "2. vinagre: sudo pacman -S vinagre"
    echo "3. remmina: sudo pacman -S remmina"
    echo ""
    echo "📱 CONEXIÓN MANUAL:"
    echo "- URL: $DISPLAY_INFO"
    echo "- Host: 127.0.0.1"
    echo "- Puerto: $(echo $DISPLAY_INFO | cut -d: -f3)"
fi

echo ""
echo "4. Información adicional..."
echo "- VM: $VM_NAME"
echo "- Estado: $(virsh list | grep $VM_NAME | awk '{print $3}')"
echo "- Display: $DISPLAY_INFO"
echo "- Configuración: qemu:///session"

echo ""
echo "🎯 ALTERNATIVAS SI NO FUNCIONA:"
echo "1. Instalar cliente VNC: sudo pacman -S virt-viewer"
echo "2. Usar navegador web con noVNC"
echo "3. Configurar GNOME Boxes para usuario"
echo ""
echo "Para detener la VM: virsh shutdown $VM_NAME"
