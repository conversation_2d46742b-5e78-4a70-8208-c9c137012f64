#!/bin/bash

# Script para crear la red virtual default

echo "🌐 CREANDO RED VIRTUAL default"
echo "==============================="

# Crear archivo XML para la red default
cat > /tmp/default.xml << 'EOF'
<network>
  <name>default</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr0' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='*************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

echo "Definiendo red virtual default..."
virsh net-define /tmp/default.xml

echo "Iniciando red virtual default..."
virsh net-start default

echo "Configurando inicio automático..."
virsh net-autostart default

echo "Verificando red creada..."
virsh net-list --all

echo "✓ Red default creada y configurada"

# Limpiar archivo temporal
rm -f /tmp/default.xml
