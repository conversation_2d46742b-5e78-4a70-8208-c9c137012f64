#!/bin/bash

# Script para crear la red virtual vm-optimized

echo "🌐 CREANDO RED VIRTUAL vm-optimized"
echo "==================================="

# Crear archivo XML para la red
cat > /tmp/vm-optimized.xml << 'EOF'
<network>
  <name>vm-optimized</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr-vmopt' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

echo "Definiendo red virtual..."
virsh net-define /tmp/vm-optimized.xml

echo "Iniciando red virtual..."
virsh net-start vm-optimized

echo "Configurando inicio automático..."
virsh net-autostart vm-optimized

echo "Verificando red creada..."
virsh net-list --all

echo "✓ Red vm-optimized creada y configurada"

# Limpiar archivo temporal
rm -f /tmp/vm-optimized.xml
