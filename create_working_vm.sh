#!/bin/bash

# Script para crear una VM que funcione correctamente con GNOME Boxes

VM_NAME="Ubuntu-Dev"
DISK_SIZE="50G"
ISO_PATH="$HOME/Imágenes/lubuntu-24.04.2-desktop-amd64.iso"
DISK_PATH="$HOME/VMs/${VM_NAME}.qcow2"

echo "🚀 CREANDO VM FUNCIONAL PARA GNOME BOXES"
echo "========================================"

# Limpiar VM existente si existe
if virsh list --all | grep -q "$VM_NAME"; then
    echo "Eliminando VM existente..."
    virsh destroy "$VM_NAME" 2>/dev/null || true
    virsh undefine "$VM_NAME" 2>/dev/null || true
fi

# Eliminar disco existente
rm -f "$DISK_PATH"

# Crear directorio para VMs
mkdir -p "$(dirname "$DISK_PATH")"

# Crear disco virtual
echo "Creando disco virtual de $DISK_SIZE..."
qemu-img create -f qcow2 "$DISK_PATH" "$DISK_SIZE"

# Crear configuración XML simple y funcional
echo "Creando configuración de VM..."
cat > /tmp/${VM_NAME}.xml << EOF
<domain type='kvm'>
  <name>$VM_NAME</name>
  <memory unit='MiB'>8192</memory>
  <currentMemory unit='MiB'>8192</currentMemory>
  <vcpu placement='static'>4</vcpu>
  <os>
    <type arch='x86_64' machine='pc-q35-6.2'>hvm</type>
    <boot dev='cdrom'/>
    <boot dev='hd'/>
  </os>
  <features>
    <acpi/>
    <apic/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='utc'/>
  <on_poweroff>destroy</on_poweroff>
  <on_reboot>restart</on_reboot>
  <on_crash>destroy</on_crash>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2'/>
      <source file='$DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <disk type='file' device='cdrom'>
      <driver name='qemu' type='raw'/>
      <source file='$ISO_PATH'/>
      <target dev='sda' bus='sata'/>
      <readonly/>
    </disk>
    <interface type='user'>
      <model type='virtio'/>
    </interface>
    <graphics type='vnc' port='-1' autoport='yes' listen='127.0.0.1'>
      <listen type='address' address='127.0.0.1'/>
    </graphics>
    <video>
      <model type='cirrus'/>
    </video>
    <input type='tablet' bus='usb'/>
    <input type='mouse' bus='ps2'/>
    <input type='keyboard' bus='ps2'/>
  </devices>
</domain>
EOF

# Definir la VM
echo "Definiendo VM en libvirt..."
virsh define /tmp/${VM_NAME}.xml

# Iniciar la VM
echo "Iniciando VM..."
virsh start "$VM_NAME"

# Esperar un momento para que se inicialice
sleep 5

# Mostrar información de conexión
echo ""
echo "✅ VM CREADA Y INICIADA EXITOSAMENTE"
echo "===================================="
echo ""
echo "📋 Información de la VM:"
echo "- Nombre: $VM_NAME"
echo "- RAM: 8GB"
echo "- CPUs: 4"
echo "- Disco: $DISK_PATH ($DISK_SIZE)"
echo "- ISO: $ISO_PATH"
echo ""

# Verificar conexión
DISPLAY_INFO=$(virsh domdisplay "$VM_NAME" 2>/dev/null)
if [ $? -eq 0 ] && [ -n "$DISPLAY_INFO" ]; then
    echo "🖥️  Display disponible: $DISPLAY_INFO"
else
    echo "🔍 Buscando puerto VNC..."
    VNC_PORT=$(ss -tlnp | grep :59 | head -1 | awk '{print $4}' | cut -d: -f2)
    if [ -n "$VNC_PORT" ]; then
        echo "🖥️  VNC disponible en: vnc://127.0.0.1:$VNC_PORT"
    else
        echo "⚠️  Display no detectado automáticamente"
    fi
fi

echo ""
echo "🎯 CÓMO CONECTAR:"
echo "1. Abrir GNOME Boxes"
echo "2. La VM '$VM_NAME' debería aparecer automáticamente"
echo "3. Hacer clic en la VM para conectar"
echo "4. Esperar a que aparezca la pantalla de Lubuntu"
echo ""
echo "🔧 Comandos útiles:"
echo "- Ver estado: virsh list"
echo "- Apagar VM: virsh shutdown $VM_NAME"
echo "- Forzar apagado: virsh destroy $VM_NAME"

# Limpiar archivo temporal
rm -f /tmp/${VM_NAME}.xml
