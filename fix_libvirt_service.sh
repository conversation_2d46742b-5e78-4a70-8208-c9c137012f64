#!/bin/bash

# Script para configurar y iniciar los servicios de libvirt correctamente

echo "🔧 CONFIGURANDO SERVICIOS LIBVIRT"
echo "================================="

echo "1. Verificando servicios disponibles..."
systemctl list-unit-files | grep libvirt

echo ""
echo "2. Iniciando servicios libvirt..."

# Iniciar libvirtd como usuario (sin sudo)
echo "Iniciando libvirtd..."
systemctl --user start libvirtd 2>/dev/null || echo "libvirtd usuario no disponible"

# Verificar si virtqemud está disponible (nuevo sistema modular)
if systemctl list-unit-files | grep -q virtqemud; then
    echo "Iniciando virtqemud (sistema modular)..."
    systemctl --user start virtqemud 2>/dev/null || echo "virtqemud usuario no disponible"
fi

# Verificar si virtnetworkd está disponible
if systemctl list-unit-files | grep -q virtnetworkd; then
    echo "Iniciando virtnetworkd..."
    systemctl --user start virtnetworkd 2>/dev/null || echo "virtnetworkd usuario no disponible"
fi

echo ""
echo "3. Verificando estado de servicios..."
systemctl --user status libvirtd 2>/dev/null || echo "libvirtd no está ejecutándose como usuario"
systemctl --user status virtqemud 2>/dev/null || echo "virtqemud no está ejecutándose como usuario"

echo ""
echo "4. Verificando conexión libvirt..."
virsh list 2>/dev/null && echo "✅ Conexión libvirt OK" || echo "❌ Problema con conexión libvirt"

echo ""
echo "5. Verificando sockets libvirt..."
ls -la /var/run/libvirt/ 2>/dev/null || echo "Directorio /var/run/libvirt/ no existe"

echo ""
echo "6. Intentando usar libvirt de usuario..."
export LIBVIRT_DEFAULT_URI="qemu:///session"
virsh list 2>/dev/null && echo "✅ Libvirt usuario OK" || echo "❌ Problema con libvirt usuario"

echo ""
echo "🎯 SOLUCIÓN ALTERNATIVA: Usar libvirt de usuario"
echo "================================================"
echo "Configurando GNOME Boxes para usar libvirt de usuario..."

# Crear configuración para usar libvirt de usuario
mkdir -p ~/.config/libvirt
echo 'uri_default = "qemu:///session"' > ~/.config/libvirt/libvirt.conf

echo ""
echo "✅ Configuración completada"
echo ""
echo "🚀 PRÓXIMOS PASOS:"
echo "1. Cerrar GNOME Boxes si está abierto"
echo "2. Ejecutar: export LIBVIRT_DEFAULT_URI=qemu:///session"
echo "3. Ejecutar: gnome-boxes"
echo "4. O usar el script de conexión alternativa"
