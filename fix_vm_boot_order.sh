#!/bin/bash

# Script para solucionar el problema de arranque de la VM

VM_NAME="Ubuntu-Dev"

echo "🔧 SOLUCIONANDO PROBLEMA DE ARRANQUE DE VM"
echo "=========================================="

# Configurar libvirt de usuario
export LIBVIRT_DEFAULT_URI="qemu:///session"

echo "1. Verificando estado actual de la VM..."
echo "========================================"

VM_STATUS=$(virsh list | grep "$VM_NAME" | awk '{print $3}')
if [ "$VM_STATUS" = "ejecutando" ]; then
    echo "✅ VM está ejecutándose"
    echo "🔄 Apagando VM para hacer cambios..."
    virsh shutdown "$VM_NAME"
    
    # Esperar a que se apague
    echo "Esperando a que la VM se apague..."
    sleep 10
    
    # Si no se apaga, forzar apagado
    if virsh list | grep -q "$VM_NAME"; then
        echo "⚡ Forzando apagado..."
        virsh destroy "$VM_NAME"
        sleep 3
    fi
    echo "✅ VM apagada"
else
    echo "✅ VM ya está apagada"
fi

echo ""
echo "2. Analizando configuración actual..."
echo "====================================="

# Exportar configuración actual
virsh dumpxml "$VM_NAME" > /tmp/${VM_NAME}_current.xml

echo "📋 Dispositivos de arranque actuales:"
grep -A 2 -B 2 "boot dev" /tmp/${VM_NAME}_current.xml

echo ""
echo "📋 Dispositivos de disco actuales:"
grep -A 5 -B 2 "device='disk'" /tmp/${VM_NAME}_current.xml

echo ""
echo "📋 Dispositivos CDROM actuales:"
grep -A 5 -B 2 "device='cdrom'" /tmp/${VM_NAME}_current.xml

echo ""
echo "3. Quitando ISO del CDROM..."
echo "============================"

# Crear configuración sin ISO
sed 's/<source file=.*lubuntu.*\.iso.*\/>//' /tmp/${VM_NAME}_current.xml > /tmp/${VM_NAME}_no_iso.xml

# También quitar la línea readonly del cdrom
sed -i '/<readonly\/>/d' /tmp/${VM_NAME}_no_iso.xml

echo "✅ ISO removido de la configuración"

echo ""
echo "4. Cambiando orden de arranque..."
echo "================================="

# Cambiar orden de arranque: primero disco duro, luego cdrom
sed -i 's/<boot dev=.cdrom.\/>//' /tmp/${VM_NAME}_no_iso.xml
sed -i 's/<boot dev=.hd.\/>//' /tmp/${VM_NAME}_no_iso.xml

# Agregar orden correcto después de </type>
sed -i '/<\/type>/a\    <boot dev="hd"/>\n    <boot dev="cdrom"/>' /tmp/${VM_NAME}_no_iso.xml

echo "✅ Orden de arranque cambiado: 1) Disco duro, 2) CDROM"

echo ""
echo "5. Aplicando cambios a la VM..."
echo "==============================="

# Redefinir la VM con la nueva configuración
virsh undefine "$VM_NAME"
virsh define /tmp/${VM_NAME}_no_iso.xml

echo "✅ VM redefinida con nueva configuración"

echo ""
echo "6. Verificando nueva configuración..."
echo "====================================="

echo "📋 Nueva configuración de arranque:"
virsh dumpxml "$VM_NAME" | grep -A 2 -B 2 "boot dev"

echo ""
echo "📋 Dispositivos de disco:"
virsh dumpxml "$VM_NAME" | grep -A 3 -B 1 "device='disk'"

echo ""
echo "7. Iniciando VM con nueva configuración..."
echo "=========================================="

virsh start "$VM_NAME"

if [ $? -eq 0 ]; then
    echo "✅ VM iniciada correctamente"
    
    # Esperar un momento y mostrar información
    sleep 5
    
    echo ""
    echo "📺 Información de conexión:"
    DISPLAY_INFO=$(virsh domdisplay "$VM_NAME" 2>/dev/null)
    if [ -n "$DISPLAY_INFO" ]; then
        echo "VNC: $DISPLAY_INFO"
    else
        echo "VNC: vnc://127.0.0.1:5900"
    fi
else
    echo "❌ Error al iniciar VM"
fi

echo ""
echo "🎉 SOLUCIÓN COMPLETADA"
echo "======================"
echo ""
echo "✅ CAMBIOS REALIZADOS:"
echo "- ❌ ISO de Lubuntu removido del CDROM"
echo "- 🥇 Disco duro configurado como primera opción de arranque"
echo "- 🥈 CDROM como segunda opción (por si necesitas reinstalar)"
echo ""
echo "🎯 RESULTADO ESPERADO:"
echo "- La VM debería arrancar directamente desde Lubuntu instalado"
echo "- No más pantalla de instalación"
echo "- Arranque directo al escritorio de Lubuntu"
echo ""
echo "🖥️  PARA CONECTAR:"
echo "- Abrir: http://localhost:8080"
echo "- O usar: vinagre $(virsh domdisplay "$VM_NAME" 2>/dev/null || echo "vnc://127.0.0.1:5900")"

# Limpiar archivos temporales
rm -f /tmp/${VM_NAME}_current.xml /tmp/${VM_NAME}_no_iso.xml

echo ""
echo "💡 NOTA: Si aún aparece la instalación, puede ser que necesites"
echo "    esperar unos segundos más o que la instalación no se completó correctamente."
