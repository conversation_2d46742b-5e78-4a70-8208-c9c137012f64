#!/bin/bash

# Script para corregir la configuración de gráficos en las plantillas VM

echo "🎨 CORRIGIENDO CONFIGURACIÓN DE GRÁFICOS"
echo "========================================"

# Función para corregir gráficos en una plantilla
fix_graphics_in_template() {
    local template_file="$1"
    local template_name="$2"
    
    echo "Corrigiendo gráficos en plantilla: $template_name"
    
    # Reemplazar la configuración de gráficos
    sed -i '/<graphics type=.*spice.*>/,/<\/graphics>/c\
    <graphics type="spice" autoport="yes">\
      <listen type="address" address="127.0.0.1"/>\
    </graphics>' "$template_file"
    
    echo "✓ Gráficos corregidos en $template_name"
}

TEMPLATES_DIR="$HOME/.local/share/vm-templates"

# Corregir todas las plantillas
for template in "$TEMPLATES_DIR"/*.xml; do
    if [ -f "$template" ]; then
        template_name=$(basename "$template" .xml)
        fix_graphics_in_template "$template" "$template_name"
    fi
done

echo ""
echo "✓ Todas las plantillas han sido corregidas"
echo "ℹ Configuración SPICE con autoport y listen en 127.0.0.1"

# Verificar una plantilla como ejemplo
echo ""
echo "📋 Verificación (plantilla desarrollo-pesado):"
grep -A 3 -B 1 "graphics" "$TEMPLATES_DIR/desarrollo-pesado.xml"
