#!/bin/bash

# Script para corregir la configuración de red de las VMs
# Cambia de network a user networking que no requiere bridges

echo "🔧 CORRIGIENDO CONFIGURACIÓN DE RED"
echo "==================================="

# Función para corregir la red en una plantilla
fix_network_in_template() {
    local template_file="$1"
    local template_name="$2"
    
    echo "Corrigiendo red en plantilla: $template_name"
    
    # Reemplazar la configuración de red con user networking
    sed -i '/<interface type=.*network.*>/,/<\/interface>/c\
    <interface type="user">\
      <model type="virtio"/>\
    </interface>' "$template_file"
    
    echo "✓ Red corregida en $template_name"
}

TEMPLATES_DIR="$HOME/.local/share/vm-templates"

# Corregir todas las plantillas
for template in "$TEMPLATES_DIR"/*.xml; do
    if [ -f "$template" ]; then
        template_name=$(basename "$template" .xml)
        fix_network_in_template "$template" "$template_name"
    fi
done

echo ""
echo "✓ Todas las plantillas han sido corregidas para usar user networking"
echo "ℹ User networking no requiere configuración especial de red"
echo "ℹ Las VMs tendrán acceso a internet pero no serán accesibles desde el host"
