#!/bin/bash

# Script para corregir la configuración SPICE de una VM existente

VM_NAME="Ubuntu-Dev"

echo "🔧 CORRIGIENDO CONFIGURACIÓN SPICE DE $VM_NAME"
echo "=============================================="

# Verificar que la VM existe
if ! virsh list --all | grep -q "$VM_NAME"; then
    echo "❌ Error: VM '$VM_NAME' no existe"
    exit 1
fi

# Asegurar que la VM está apagada
if virsh list | grep -q "$VM_NAME"; then
    echo "Apagando VM..."
    virsh destroy "$VM_NAME"
    sleep 2
fi

# Exportar configuración actual
echo "Exportando configuración actual..."
virsh dumpxml "$VM_NAME" > /tmp/${VM_NAME}_original.xml

# Crear configuración corregida
echo "Corrigiendo configuración SPICE..."
sed 's/<listen type=.*none.*\/>/<listen type="address" address="127.0.0.1"\/>/' /tmp/${VM_NAME}_original.xml > /tmp/${VM_NAME}_fixed.xml

# También asegurar que autoport esté configurado
sed -i 's/<graphics type=.spice.>/<graphics type="spice" autoport="yes">/' /tmp/${VM_NAME}_fixed.xml

# Redefinir la VM con la configuración corregida
echo "Redefiniendo VM con configuración corregida..."
virsh undefine "$VM_NAME"
virsh define /tmp/${VM_NAME}_fixed.xml

echo "✅ VM redefinida con configuración SPICE corregida"

# Verificar la configuración
echo ""
echo "📋 Verificando configuración SPICE:"
virsh dumpxml "$VM_NAME" | grep -A 3 -B 1 graphics

# Limpiar archivos temporales
rm -f /tmp/${VM_NAME}_original.xml /tmp/${VM_NAME}_fixed.xml

echo ""
echo "🚀 Listo para iniciar la VM:"
echo "vm-manager start $VM_NAME"
