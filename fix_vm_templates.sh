#!/bin/bash

# Script para corregir las plantillas de VMs
# Agrega la sección <os> faltante que es obligatoria

echo "🔧 CORRIGIENDO PLANTILLAS DE VMs"
echo "==============================="

# Colores
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

TEMPLATES_DIR="$HOME/.local/share/vm-templates"

# Función para corregir una plantilla
fix_template() {
    local template_file="$1"
    local template_name="$2"
    
    show_info "Corrigiendo plantilla: $template_name"
    
    # Crear plantilla corregida
    cat > "$template_file" << EOF
<domain type='kvm'>
  <name>TEMPLATE-${template_name}</name>
  <memory unit='MiB'>MEMORY_SIZE</memory>
  <currentMemory unit='MiB'>MEMORY_SIZE</currentMemory>
  <vcpu placement='static'>CPU_CORES</vcpu>
  <os>
    <type arch='x86_64' machine='pc-q35-6.2'>hvm</type>
    <boot dev='cdrom'/>
    <boot dev='hd'/>
  </os>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <interface type='network'>
      <source network='vm-optimized'/>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'/>
    <video>
      <model type='qxl' ram='VIDEO_RAM' vram='VIDEO_RAM' vgamem='VIDEO_VRAM' heads='VIDEO_HEADS'/>
    </video>
    <memballoon model='virtio'/>
  </devices>
</domain>
EOF
}

# Corregir todas las plantillas
show_info "Creando plantillas corregidas..."

# 1. Desarrollo Ligero (4GB RAM, 2 cores)
fix_template "$TEMPLATES_DIR/desarrollo-ligero.xml" "Desarrollo-Ligero"
sed -i 's/MEMORY_SIZE/4096/g; s/CPU_CORES/2/g; s/VIDEO_RAM/65536/g; s/VIDEO_VRAM/16384/g; s/VIDEO_HEADS/1/g' "$TEMPLATES_DIR/desarrollo-ligero.xml"

# 2. Desarrollo Pesado (8GB RAM, 4 cores)
fix_template "$TEMPLATES_DIR/desarrollo-pesado.xml" "Desarrollo-Pesado"
sed -i 's/MEMORY_SIZE/8192/g; s/CPU_CORES/4/g; s/VIDEO_RAM/131072/g; s/VIDEO_VRAM/32768/g; s/VIDEO_HEADS/1/g' "$TEMPLATES_DIR/desarrollo-pesado.xml"

# 3. Servidor (12GB RAM, 6 cores)
fix_template "$TEMPLATES_DIR/servidor.xml" "Servidor"
sed -i 's/MEMORY_SIZE/12288/g; s/CPU_CORES/6/g; s/VIDEO_RAM/65536/g; s/VIDEO_VRAM/16384/g; s/VIDEO_HEADS/1/g' "$TEMPLATES_DIR/servidor.xml"

# 4. Alto Rendimiento (16GB RAM, 8 cores)
fix_template "$TEMPLATES_DIR/alto-rendimiento.xml" "Alto-Rendimiento"
sed -i 's/MEMORY_SIZE/16384/g; s/CPU_CORES/8/g; s/VIDEO_RAM/262144/g; s/VIDEO_VRAM/65536/g; s/VIDEO_HEADS/2/g' "$TEMPLATES_DIR/alto-rendimiento.xml"

# 5. Testing (6GB RAM, 3 cores)
fix_template "$TEMPLATES_DIR/testing.xml" "Testing"
sed -i 's/MEMORY_SIZE/6144/g; s/CPU_CORES/3/g; s/VIDEO_RAM/65536/g; s/VIDEO_VRAM/16384/g; s/VIDEO_HEADS/1/g' "$TEMPLATES_DIR/testing.xml"

show_message "Todas las plantillas han sido corregidas"

# Actualizar el script vm-manager para manejar mejor los errores
show_info "Actualizando vm-manager..."

# Crear versión corregida del vm-manager
cat > ~/.local/bin/vm-manager << 'EOF'
#!/bin/bash

# Gestor de plantillas de VMs optimizadas para 40GB RAM
# Permite crear VMs basadas en plantillas predefinidas

TEMPLATES_DIR="$HOME/.local/share/vm-templates"
VMS_DIR="/home/<USER>/VMs"

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

show_usage() {
    echo "Uso: vm-manager <comando> [opciones]"
    echo ""
    echo "Comandos disponibles:"
    echo "  list-templates    - Listar plantillas disponibles"
    echo "  create <template> <nombre> <disco_gb> [iso_path] - Crear VM desde plantilla"
    echo "  list-vms         - Listar VMs existentes"
    echo "  start <vm>       - Iniciar VM"
    echo "  stop <vm>        - Detener VM"
    echo "  delete <vm>      - Eliminar VM"
    echo "  info <vm>        - Mostrar información de VM"
    echo ""
    echo "Plantillas disponibles:"
    echo "  desarrollo-ligero  - 4GB RAM, 2 cores (desarrollo básico)"
    echo "  desarrollo-pesado  - 8GB RAM, 4 cores (IDEs pesados, compilación)"
    echo "  servidor          - 12GB RAM, 6 cores (servicios, bases de datos)"
    echo "  alto-rendimiento  - 16GB RAM, 8 cores (máximo rendimiento)"
    echo "  testing           - 6GB RAM, 3 cores (pruebas, laboratorio)"
    echo ""
    echo "Ejemplos:"
    echo "  vm-manager create desarrollo-pesado Ubuntu-Dev 50 ~/Downloads/ubuntu.iso"
    echo "  vm-manager start Ubuntu-Dev"
    echo "  vm-manager list-vms"
}

list_templates() {
    echo -e "${BLUE}Plantillas disponibles:${NC}"
    for template in "$TEMPLATES_DIR"/*.xml; do
        if [ -f "$template" ]; then
            basename "$template" .xml | sed 's/^/  - /'
        fi
    done
}

create_vm() {
    local template="$1"
    local vm_name="$2"
    local disk_size="$3"
    local iso_path="$4"
    
    if [ -z "$template" ] || [ -z "$vm_name" ] || [ -z "$disk_size" ]; then
        echo -e "${RED}Error: Faltan parámetros${NC}"
        show_usage
        return 1
    fi
    
    local template_file="$TEMPLATES_DIR/${template}.xml"
    if [ ! -f "$template_file" ]; then
        echo -e "${RED}Error: Plantilla '$template' no encontrada${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Creando VM '$vm_name' desde plantilla '$template'${NC}"
    
    # Crear disco
    local disk_path="$VMS_DIR/${vm_name}.qcow2"
    echo "Creando disco de ${disk_size}GB..."
    qemu-img create -f qcow2 "$disk_path" "${disk_size}G"
    
    # Crear configuración XML
    local vm_xml="/tmp/${vm_name}.xml"
    sed "s/TEMPLATE-[^<]*/${vm_name}/g; s|DISK_PATH|${disk_path}|g" "$template_file" > "$vm_xml"
    
    # Agregar ISO si se proporciona
    if [ -n "$iso_path" ] && [ -f "$iso_path" ]; then
        echo "Agregando ISO: $iso_path"
        # Insertar configuración de CDROM antes del cierre de devices
        sed -i "/<\/devices>/i\\    <disk type='file' device='cdrom'>\\n      <driver name='qemu' type='raw'/>\\n      <source file='${iso_path}'/>\\n      <target dev='hdc' bus='ide'/>\\n      <readonly/>\\n    </disk>" "$vm_xml"
    fi
    
    # Definir VM
    echo "Definiendo VM en libvirt..."
    if virsh define "$vm_xml"; then
        echo -e "${GREEN}✓ VM '$vm_name' creada exitosamente${NC}"
        echo "Disco: $disk_path"
        echo "Para iniciar: vm-manager start $vm_name"
        echo "Para conectar: virt-viewer $vm_name"
    else
        echo -e "${RED}✗ Error al definir la VM${NC}"
        echo "Revisa el archivo XML: $vm_xml"
        return 1
    fi
    
    # Limpiar archivo temporal solo si fue exitoso
    # rm -f "$vm_xml"
}

list_vms() {
    echo -e "${BLUE}VMs existentes:${NC}"
    virsh list --all --name | while read vm; do
        if [ -n "$vm" ]; then
            local state=$(virsh domstate "$vm" 2>/dev/null)
            case $state in
                "running") echo -e "  ${GREEN}●${NC} $vm (ejecutándose)" ;;
                "shut off") echo -e "  ${RED}●${NC} $vm (apagada)" ;;
                "paused") echo -e "  ${YELLOW}●${NC} $vm (pausada)" ;;
                *) echo -e "  ${BLUE}●${NC} $vm ($state)" ;;
            esac
        fi
    done
}

start_vm() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Iniciando VM '$vm_name'${NC}"
    if virsh start "$vm_name"; then
        echo -e "${GREEN}✓ VM iniciada${NC}"
        echo "Para conectar: virt-viewer $vm_name"
    else
        echo -e "${RED}✗ Error al iniciar VM${NC}"
    fi
}

stop_vm() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Deteniendo VM '$vm_name'${NC}"
    virsh shutdown "$vm_name"
}

delete_vm() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}¿Estás seguro de eliminar la VM '$vm_name'? (s/N)${NC}"
    read -r confirm
    if [ "$confirm" = "s" ] || [ "$confirm" = "S" ]; then
        virsh destroy "$vm_name" 2>/dev/null
        virsh undefine "$vm_name"
        rm -f "$VMS_DIR/${vm_name}.qcow2"
        echo -e "${GREEN}✓ VM '$vm_name' eliminada${NC}"
    else
        echo "Operación cancelada"
    fi
}

vm_info() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Información de VM '$vm_name':${NC}"
    virsh dominfo "$vm_name"
}

# Comando principal
case "$1" in
    list-templates)
        list_templates
        ;;
    create)
        create_vm "$2" "$3" "$4" "$5"
        ;;
    list-vms)
        list_vms
        ;;
    start)
        start_vm "$2"
        ;;
    stop)
        stop_vm "$2"
        ;;
    delete)
        delete_vm "$2"
        ;;
    info)
        vm_info "$2"
        ;;
    *)
        show_usage
        ;;
esac
EOF

chmod +x ~/.local/bin/vm-manager

show_message "vm-manager actualizado"

echo ""
echo -e "${GREEN}🎉 PLANTILLAS CORREGIDAS${NC}"
echo -e "${BLUE}Ahora puedes crear VMs sin errores${NC}"
echo ""
echo "Prueba nuevamente:"
echo "vm-manager create desarrollo-pesado Ubuntu-Dev 50 ~/Imágenes/lubuntu-24.04.2-desktop-amd64.iso"
