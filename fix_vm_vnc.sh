#!/bin/bash

# Script para configurar la VM con VNC en lugar de SPICE

VM_NAME="Ubuntu-Dev"

echo "🔧 CONFIGURANDO VM CON VNC"
echo "=========================="

# Verificar que la VM existe
if ! virsh list --all | grep -q "$VM_NAME"; then
    echo "❌ Error: VM '$VM_NAME' no existe"
    exit 1
fi

# Asegurar que la VM está apagada
if virsh list | grep -q "$VM_NAME"; then
    echo "Apagando VM..."
    virsh destroy "$VM_NAME"
    sleep 2
fi

# Exportar configuración actual
echo "Exportando configuración actual..."
virsh dumpxml "$VM_NAME" > /tmp/${VM_NAME}_original.xml

# Crear configuración con VNC
echo "Configurando VNC..."
sed 's/<graphics type=.spice.*/<graphics type="vnc" port="-1" autoport="yes" listen="127.0.0.1">/' /tmp/${VM_NAME}_original.xml > /tmp/${VM_NAME}_vnc.xml

# Asegurar que el listen está bien configurado
sed -i 's/<listen type=.*address.*address=.*127.0.0.1.*\/>/<listen type="address" address="127.0.0.1"\/>/' /tmp/${VM_NAME}_vnc.xml

# Redefinir la VM
echo "Redefiniendo VM con VNC..."
virsh undefine "$VM_NAME"
virsh define /tmp/${VM_NAME}_vnc.xml

echo "✅ VM configurada con VNC"

# Verificar la configuración
echo ""
echo "📋 Verificando configuración VNC:"
virsh dumpxml "$VM_NAME" | grep -A 3 -B 1 graphics

# Limpiar archivos temporales
rm -f /tmp/${VM_NAME}_original.xml /tmp/${VM_NAME}_vnc.xml

echo ""
echo "🚀 Iniciando VM con VNC..."
virsh start "$VM_NAME"

# Esperar un momento y mostrar información de conexión
sleep 3
echo ""
echo "📺 Información de conexión:"
virsh domdisplay "$VM_NAME" || echo "VNC en puerto automático - verificar con: ss -tlnp | grep :59"
