#!/bin/bash

# Script de optimización GNOME Boxes para sistema con 40GB RAM
# Configura GNOME Boxes y libvirt para máximo rendimiento

echo "📦 OPTIMIZANDO GNOME BOXES PARA 40GB RAM"
echo "========================================"

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

show_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

show_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Verificar que libvirt esté funcionando
show_header "VERIFICANDO SERVICIOS"
if systemctl is-active --quiet libvirtd; then
    show_message "Servicio libvirtd activo"
else
    show_warning "Iniciando servicio libvirtd..."
    sudo systemctl start libvirtd
fi

# Configurar libvirt para máximo rendimiento
show_header "CONFIGURANDO LIBVIRT PARA 40GB RAM"

show_info "Creando configuración optimizada de libvirt..."

# Configuración de qemu optimizada
sudo mkdir -p /etc/libvirt/qemu/conf
sudo tee /etc/libvirt/qemu.conf << 'EOF'
# Configuración QEMU optimizada para 40GB RAM

# Usuario y grupo para QEMU
user = "root"
group = "kvm"

# Configuración de memoria
max_files = 32768
max_processes = 131072

# Configuración de CPU
cgroup_device_acl = [
    "/dev/null", "/dev/full", "/dev/zero",
    "/dev/random", "/dev/urandom",
    "/dev/ptmx", "/dev/kvm", "/dev/kqemu",
    "/dev/rtc","/dev/hpet", "/dev/vfio/vfio"
]

# Configuración de red
clear_emulator_capabilities = 0
allow_disk_format_probing = 1

# Configuración de memoria hugepages
hugetlbfs_mount = "/dev/hugepages"

# Configuración de seguridad
security_driver = "none"
security_default_confined = 0
security_require_confined = 0

# Configuración de logging
log_level = 2
log_outputs = "2:file:/var/log/libvirt/qemu.log"

# Configuración de memoria para VMs grandes
max_queued = 1000
keepalive_interval = 5
keepalive_count = 5
EOF

show_message "Configuración de QEMU creada"

# Configuración de libvirtd optimizada
show_info "Configurando daemon libvirtd..."

sudo tee /etc/libvirt/libvirtd.conf << 'EOF'
# Configuración libvirtd optimizada para 40GB RAM

# Configuración de red
listen_tls = 0
listen_tcp = 0
unix_sock_group = "libvirt"
unix_sock_ro_perms = "0777"
unix_sock_rw_perms = "0770"
unix_sock_admin_perms = "0700"
unix_sock_dir = "/var/run/libvirt"

# Configuración de autenticación
auth_unix_ro = "none"
auth_unix_rw = "none"

# Configuración de logging
log_level = 2
log_outputs = "2:file:/var/log/libvirt/libvirtd.log"

# Configuración de procesos
max_clients = 5000
max_workers = 20
max_requests = 1000
max_client_requests = 5

# Configuración de memoria
keepalive_interval = 5
keepalive_count = 5
keepalive_required = 1

# Configuración de auditoría
audit_level = 2
audit_logging = 0

# Configuración de host UUID
host_uuid_source = "machine-id"
EOF

show_message "Configuración de libvirtd creada"

# Crear red optimizada para VMs
show_header "CONFIGURANDO RED OPTIMIZADA"

show_info "Creando red virtual optimizada..."

sudo tee /tmp/vm-network.xml << 'EOF'
<network>
  <name>vm-optimized</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <mac address='52:54:00:12:34:56'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='*************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Definir y activar la red
sudo virsh net-define /tmp/vm-network.xml 2>/dev/null || true
sudo virsh net-autostart vm-optimized 2>/dev/null || true
sudo virsh net-start vm-optimized 2>/dev/null || true

show_message "Red virtual optimizada creada"

# Configurar pool de almacenamiento optimizado
show_header "CONFIGURANDO ALMACENAMIENTO"

show_info "Creando pool de almacenamiento optimizado..."

# Crear directorio para VMs
sudo mkdir -p /home/<USER>/VMs
sudo chown $USER:$USER /home/<USER>/VMs

# Definir pool de almacenamiento
sudo tee /tmp/vm-storage.xml << 'EOF'
<pool type='dir'>
  <name>vm-storage</name>
  <target>
    <path>/home/<USER>/VMs</path>
    <permissions>
      <mode>0755</mode>
      <owner>1000</owner>
      <group>1000</group>
    </permissions>
  </target>
</pool>
EOF

sudo virsh pool-define /tmp/vm-storage.xml 2>/dev/null || true
sudo virsh pool-autostart vm-storage 2>/dev/null || true
sudo virsh pool-start vm-storage 2>/dev/null || true

show_message "Pool de almacenamiento creado en /home/<USER>/VMs"

# Crear configuraciones de GNOME Boxes
show_header "CONFIGURANDO GNOME BOXES"

show_info "Creando configuraciones optimizadas..."

# Configuración de dconf para GNOME Boxes
mkdir -p ~/.config/dconf
cat > ~/.config/dconf/boxes << 'EOF'
[org/gnome/boxes]
first-run=false
view='icon-view'
window-maximized=true

[org/gnome/boxes/preferences]
default-ram-size=8192
default-disk-size=50
enable-shared-folders=true
auto-redirection=true
EOF

show_message "Configuración de GNOME Boxes creada"

# Crear script de creación de VMs optimizadas
show_info "Creando script de creación de VMs..."

cat > ~/.local/bin/create-optimized-vm << 'EOF'
#!/bin/bash

# Script para crear VMs optimizadas en GNOME Boxes
# Uso: create-optimized-vm <nombre> <ram_gb> <disk_gb> <iso_path>

if [ $# -ne 4 ]; then
    echo "Uso: $0 <nombre> <ram_gb> <disk_gb> <iso_path>"
    echo "Ejemplo: $0 Ubuntu-Dev 8 50 ~/Downloads/ubuntu.iso"
    exit 1
fi

VM_NAME="$1"
RAM_GB="$2"
DISK_GB="$3"
ISO_PATH="$4"

echo "Creando VM optimizada: $VM_NAME"
echo "RAM: ${RAM_GB}GB, Disco: ${DISK_GB}GB"

# Crear disco virtual
qemu-img create -f qcow2 "/home/<USER>/VMs/${VM_NAME}.qcow2" "${DISK_GB}G"

# Crear configuración XML de la VM
cat > "/tmp/${VM_NAME}.xml" << VMEOF
<domain type='kvm'>
  <name>${VM_NAME}</name>
  <memory unit='MiB'>$((RAM_GB * 1024))</memory>
  <currentMemory unit='MiB'>$((RAM_GB * 1024))</currentMemory>
  <vcpu placement='static'>4</vcpu>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='/home/<USER>/VMs/${VM_NAME}.qcow2'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <disk type='file' device='cdrom'>
      <driver name='qemu' type='raw'/>
      <source file='${ISO_PATH}'/>
      <target dev='hdc' bus='ide'/>
      <readonly/>
    </disk>
    <interface type='network'>
      <source network='vm-optimized'/>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'/>
    <video>
      <model type='qxl' ram='65536' vram='65536' vgamem='16384' heads='1'/>
    </video>
  </devices>
</domain>
VMEOF

# Definir la VM
virsh define "/tmp/${VM_NAME}.xml"

echo "✓ VM $VM_NAME creada exitosamente"
echo "Puedes iniciarla con: virsh start $VM_NAME"
echo "O usar GNOME Boxes para gestionarla"
EOF

chmod +x ~/.local/bin/create-optimized-vm
show_message "Script de creación de VMs disponible: create-optimized-vm"

# Reiniciar servicios
show_header "REINICIANDO SERVICIOS"
sudo systemctl restart libvirtd
show_message "Servicios reiniciados"

# Limpiar archivos temporales
rm -f /tmp/vm-network.xml /tmp/vm-storage.xml

show_header "CONFIGURACIÓN COMPLETADA"
show_info "GNOME Boxes optimizado para 40GB RAM"
show_info "Directorio de VMs: /home/<USER>/VMs"
show_info "Red optimizada: vm-optimized (192.168.100.x)"
show_info "Script de creación: create-optimized-vm"

echo ""
echo -e "${GREEN}🎉 GNOME BOXES OPTIMIZADO${NC}"
echo -e "${BLUE}Reinicia la sesión para aplicar permisos de grupo${NC}"
