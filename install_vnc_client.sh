#!/bin/bash

# Script para instalar cliente VNC y conectar a la VM

echo "📦 INSTALANDO CLIENTE VNC"
echo "========================="

echo "Instalando vinagre (cliente VNC ligero)..."
echo "Nota: Se requiere contraseña de sudo"

# Intentar instalar vinagre
if sudo pacman -S vinagre --noconfirm; then
    echo "✅ vinagre instalado correctamente"
    
    echo ""
    echo "🚀 CONECTANDO A LA VM..."
    
    # Configurar libvirt de usuario
    export LIBVIRT_DEFAULT_URI="qemu:///session"
    
    # Obtener información de display
    DISPLAY_INFO=$(virsh domdisplay Ubuntu-Dev 2>/dev/null)
    
    if [ -n "$DISPLAY_INFO" ]; then
        echo "Conectando a: $DISPLAY_INFO"
        vinagre "$DISPLAY_INFO" &
        echo "✅ vinagre iniciado - debería abrirse la ventana de la VM"
    else
        echo "❌ No se pudo obtener información de display"
        echo "Intentando conexión manual a vnc://127.0.0.1:5900"
        vinagre vnc://127.0.0.1:5900 &
    fi
    
else
    echo "❌ Error al instalar vinagre"
    echo ""
    echo "🔧 ALTERNATIVAS:"
    echo "1. Instalar manualmente: sudo pacman -S vinagre"
    echo "2. Usar otro cliente: sudo pacman -S remmina"
    echo "3. Usar virt-viewer: sudo pacman -S virt-viewer"
    echo ""
    echo "📱 CONEXIÓN MANUAL:"
    echo "- Host: 127.0.0.1"
    echo "- Puerto: 5900 (o el que muestre: virsh domdisplay Ubuntu-Dev)"
    echo "- Protocolo: VNC"
fi

echo ""
echo "ℹ️  INFORMACIÓN IMPORTANTE:"
echo "- La VM Ubuntu-Dev está ejecutándose"
echo "- Deberías ver la pantalla de arranque de Lubuntu"
echo "- Si ves pantalla negra, espera 1-2 minutos"
echo "- Para apagar la VM: virsh shutdown Ubuntu-Dev"
