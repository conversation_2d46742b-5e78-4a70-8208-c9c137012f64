#!/bin/bash

# Script para migrar la VM al espacio de usuario para GNOME Boxes

VM_NAME="Ubuntu-Dev"

echo "🔄 MIGRANDO VM AL ESPACIO DE USUARIO"
echo "===================================="

# Configurar libvirt de usuario
export LIBVIRT_DEFAULT_URI="qemu:///session"

echo "1. Verificando VM en sistema global..."
virsh -c qemu:///system list --all | grep "$VM_NAME" && echo "✅ VM encontrada en sistema global"

echo ""
echo "2. Exportando configuración de VM..."
virsh -c qemu:///system dumpxml "$VM_NAME" > /tmp/${VM_NAME}_export.xml

echo ""
echo "3. Apagando VM en sistema global..."
virsh -c qemu:///system destroy "$VM_NAME" 2>/dev/null || echo "VM ya estaba apagada"

echo ""
echo "4. Definiendo VM en espacio de usuario..."
virsh -c qemu:///session define /tmp/${VM_NAME}_export.xml

echo ""
echo "5. Eliminando VM del sistema global..."
virsh -c qemu:///system undefine "$VM_NAME"

echo ""
echo "6. Iniciando VM en espacio de usuario..."
virsh -c qemu:///session start "$VM_NAME"

echo ""
echo "7. Verificando VM en espacio de usuario..."
virsh -c qemu:///session list --all

echo ""
echo "✅ MIGRACIÓN COMPLETADA"
echo "======================"
echo ""
echo "🎯 AHORA EJECUTAR GNOME BOXES:"
echo "export LIBVIRT_DEFAULT_URI=qemu:///session"
echo "gnome-boxes"
echo ""
echo "O usar el script de inicio:"

# Crear script de inicio para GNOME Boxes
cat > start_gnome_boxes.sh << 'EOF'
#!/bin/bash
export LIBVIRT_DEFAULT_URI="qemu:///session"
gnome-boxes
EOF

chmod +x start_gnome_boxes.sh

echo "✅ Script creado: ./start_gnome_boxes.sh"

# Limpiar archivo temporal
rm -f /tmp/${VM_NAME}_export.xml
