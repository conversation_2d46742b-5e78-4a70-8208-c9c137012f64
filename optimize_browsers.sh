#!/bin/bash

# Script para optimizar navegadores web para 40GB RAM

echo "🌐 Optimizando navegadores para aprovechar 40GB RAM..."

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

show_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

show_warning() {
    echo -e "${YELLOW}[ADVERTENCIA]${NC} $1"
}

# Optimizar Firefox
show_message "Configurando Firefox..."

# Crear directorio de configuración si no existe
mkdir -p ~/.mozilla/firefox

# Buscar perfil de Firefox
FIREFOX_PROFILE=$(find ~/.mozilla/firefox -name "*.default*" -type d | head -1)

if [ -n "$FIREFOX_PROFILE" ]; then
    show_message "Perfil de Firefox encontrado: $FIREFOX_PROFILE"
    
    # Crear user.js con optimizaciones
    cat > "$FIREFOX_PROFILE/user.js" << 'EOF'
// Optimizaciones de Firefox para 40GB RAM

// Configuración de memoria
user_pref("browser.cache.memory.capacity", 2097152); // 2GB cache en RAM
user_pref("browser.cache.memory.max_entry_size", 524288); // 512MB max por entrada
user_pref("browser.sessionhistory.max_total_viewers", 8); // Más páginas en memoria

// Configuración de procesos
user_pref("dom.ipc.processCount", 12); // Más procesos de contenido
user_pref("dom.ipc.processCount.webIsolated", 8);

// Configuración de red
user_pref("network.http.max-connections", 256);
user_pref("network.http.max-connections-per-server", 32);
user_pref("network.http.max-persistent-connections-per-server", 16);

// Configuración de JavaScript
user_pref("javascript.options.mem.high_water_mark", 128); // 128MB para JS
user_pref("javascript.options.mem.max", 2147483648); // 2GB max para JS

// Configuración de WebGL y hardware
user_pref("webgl.force-enabled", true);
user_pref("layers.acceleration.force-enabled", true);
user_pref("gfx.webrender.all", true);

// Configuración de preload
user_pref("network.dns.disablePrefetch", false);
user_pref("network.prefetch-next", true);
user_pref("browser.cache.use_new_backend", 1);

// Configuración de multimedia
user_pref("media.memory_cache_max_size", 1048576); // 1GB para multimedia
user_pref("media.cache_readahead_limit", 7200); // 2 horas de buffer

// Configuración de seguridad sin sacrificar rendimiento
user_pref("security.sandbox.content.level", 3);
user_pref("dom.security.https_only_mode", true);
EOF

    show_message "Firefox optimizado para usar más RAM"
else
    show_warning "No se encontró perfil de Firefox"
fi

# Optimizar Brave
show_message "Configurando Brave..."

# Crear script de lanzamiento optimizado para Brave
cat > ~/.local/bin/brave-optimized << 'EOF'
#!/bin/bash
# Lanzador optimizado de Brave para 40GB RAM

exec /usr/bin/brave \
    --memory-pressure-off \
    --max_old_space_size=8192 \
    --js-flags="--max-old-space-size=8192" \
    --aggressive-cache-discard \
    --enable-features=VaapiVideoDecoder \
    --use-gl=desktop \
    --enable-gpu-rasterization \
    --enable-zero-copy \
    --disable-background-timer-throttling \
    --disable-renderer-backgrounding \
    --disable-backgrounding-occluded-windows \
    --process-per-site \
    --site-per-process \
    "$@"
EOF

chmod +x ~/.local/bin/brave-optimized

show_message "Brave optimizado para usar más RAM"

# Crear configuración para Chrome/Chromium si está instalado
if command -v google-chrome &> /dev/null; then
    show_message "Configurando Google Chrome..."
    
    cat > ~/.local/bin/chrome-optimized << 'EOF'
#!/bin/bash
# Lanzador optimizado de Chrome para 40GB RAM

exec /usr/bin/google-chrome \
    --memory-pressure-off \
    --max_old_space_size=8192 \
    --js-flags="--max-old-space-size=8192" \
    --aggressive-cache-discard \
    --enable-features=VaapiVideoDecoder \
    --use-gl=desktop \
    --enable-gpu-rasterization \
    --enable-zero-copy \
    --disable-background-timer-throttling \
    --disable-renderer-backgrounding \
    --disable-backgrounding-occluded-windows \
    --process-per-site \
    --site-per-process \
    "$@"
EOF
    
    chmod +x ~/.local/bin/chrome-optimized
    show_message "Chrome optimizado para usar más RAM"
fi

echo ""
show_message "¡Navegadores optimizados exitosamente!"
show_message "Reinicia los navegadores para aplicar los cambios"
show_message "Usa 'brave-optimized' para lanzar Brave optimizado"
