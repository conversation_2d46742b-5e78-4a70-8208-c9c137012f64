#!/bin/bash

# Script para recrear las plantillas VM con configuración completa y correcta

echo "🔧 RECREANDO PLANTILLAS VM COMPLETAS"
echo "===================================="

TEMPLATES_DIR="$HOME/.local/share/vm-templates"

# Crear directorio si no existe
mkdir -p "$TEMPLATES_DIR"

# Función para crear una plantilla completa
create_template() {
    local name="$1"
    local memory="$2"
    local vcpus="$3"
    local file="$TEMPLATES_DIR/${name}.xml"
    
    echo "Creando plantilla: $name (${memory}MB RAM, ${vcpus} CPUs)"
    
    cat > "$file" << EOF
<domain type='kvm'>
  <name>TEMPLATE-${name^}</name>
  <memory unit='MiB'>${memory}</memory>
  <currentMemory unit='MiB'>${memory}</currentMemory>
  <vcpu placement='static'>${vcpus}</vcpu>
  <os>
    <type arch='x86_64' machine='pc-q35-6.2'>hvm</type>
    <boot dev='cdrom'/>
    <boot dev='hd'/>
  </os>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <interface type='user'>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'>
      <listen type='address' address='127.0.0.1'/>
    </graphics>
    <video>
      <model type='qxl' ram='131072' vram='131072' vgamem='32768' heads='1'/>
    </video>
    <memballoon model='virtio'/>
  </devices>
</domain>
EOF
    
    echo "✓ Plantilla $name creada"
}

# Crear todas las plantillas
create_template "desarrollo-ligero" "4096" "2"
create_template "desarrollo-pesado" "8192" "4"
create_template "servidor" "12288" "6"
create_template "alto-rendimiento" "16384" "8"
create_template "testing" "6144" "3"

echo ""
echo "✅ Todas las plantillas han sido recreadas correctamente"
echo ""
echo "📋 Plantillas disponibles:"
ls -la "$TEMPLATES_DIR"
