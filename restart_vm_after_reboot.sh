#!/bin/bash

# Script para reactivar la VM después de un reinicio del sistema

echo "🔄 REACTIVANDO VM DESPUÉS DEL REINICIO"
echo "======================================"

VM_NAME="Ubuntu-Dev"

echo "1. Verificando servicios del sistema..."
echo "======================================="

# Verificar si libvirt está disponible
echo "🔍 Verificando libvirt..."
if systemctl list-unit-files | grep -q libvirtd; then
    echo "✅ libvirtd disponible"
else
    echo "❌ libvirtd no encontrado"
fi

# Verificar estado de KVM
echo ""
echo "🔍 Verificando KVM..."
if lsmod | grep -q kvm; then
    echo "✅ Módulos KVM cargados"
    lsmod | grep kvm
else
    echo "⚠️  Cargando módulos KVM..."
    sudo modprobe kvm
    sudo modprobe kvm_intel  # o kvm_amd según tu procesador
fi

echo ""
echo "2. Configurando libvirt de usuario..."
echo "====================================="

# Configurar libvirt de usuario
export LIBVIRT_DEFAULT_URI="qemu:///session"
echo "✅ Variable LIBVIRT_DEFAULT_URI configurada"

# Crear configuración de libvirt si no existe
mkdir -p ~/.config/libvirt
echo 'uri_default = "qemu:///session"' > ~/.config/libvirt/libvirt.conf
echo "✅ Configuración de libvirt creada"

echo ""
echo "3. Verificando estado de la VM..."
echo "================================="

# Verificar si la VM existe
if virsh list --all | grep -q "$VM_NAME"; then
    echo "✅ VM '$VM_NAME' encontrada"
    
    # Verificar estado actual
    VM_STATE=$(virsh list --all | grep "$VM_NAME" | awk '{print $3}')
    echo "Estado actual: $VM_STATE"
    
    if [ "$VM_STATE" = "ejecutando" ]; then
        echo "✅ VM ya está ejecutándose"
    else
        echo "🚀 Iniciando VM..."
        virsh start "$VM_NAME"
        sleep 5
        
        # Verificar que se inició correctamente
        if virsh list | grep -q "$VM_NAME"; then
            echo "✅ VM iniciada correctamente"
        else
            echo "❌ Error al iniciar VM"
            echo "Verificando logs..."
            journalctl -u libvirtd --since "1 minute ago" | tail -10
        fi
    fi
else
    echo "❌ VM '$VM_NAME' no encontrada"
    echo "🔍 VMs disponibles:"
    virsh list --all
    echo ""
    echo "💡 Puede que necesites recrear la VM"
fi

echo ""
echo "4. Verificando conectividad VNC..."
echo "=================================="

if virsh list | grep -q "$VM_NAME"; then
    # Obtener información de display
    DISPLAY_INFO=$(virsh domdisplay "$VM_NAME" 2>/dev/null)
    
    if [ -n "$DISPLAY_INFO" ]; then
        echo "✅ Display VNC disponible: $DISPLAY_INFO"
        
        # Verificar puerto VNC
        VNC_PORT=$(echo "$DISPLAY_INFO" | grep -o ':[0-9]*' | cut -d: -f2)
        ACTUAL_PORT=$((5900 + VNC_PORT))
        
        if ss -tlnp | grep -q ":$ACTUAL_PORT"; then
            echo "✅ Puerto VNC $ACTUAL_PORT está escuchando"
        else
            echo "⚠️  Puerto VNC no detectado, esperando..."
            sleep 3
            if ss -tlnp | grep -q ":$ACTUAL_PORT"; then
                echo "✅ Puerto VNC $ACTUAL_PORT ahora disponible"
            else
                echo "❌ Puerto VNC no disponible"
            fi
        fi
    else
        echo "❌ No se pudo obtener información de display"
    fi
else
    echo "❌ VM no está ejecutándose"
fi

echo ""
echo "5. Resumen del estado..."
echo "========================"

echo "VM: $VM_NAME"
echo "Estado: $(virsh list --all | grep "$VM_NAME" | awk '{print $3}' || echo 'No encontrada')"
echo "Display: $(virsh domdisplay "$VM_NAME" 2>/dev/null || echo 'No disponible')"
echo "Configuración: $LIBVIRT_DEFAULT_URI"

echo ""
echo "🎯 PRÓXIMOS PASOS:"
echo "=================="

if virsh list | grep -q "$VM_NAME"; then
    echo "✅ VM está lista para usar"
    echo "🖥️  Para conectar:"
    echo "   - Instalar cliente VNC: sudo pacman -S vinagre"
    echo "   - Conectar: vinagre $(virsh domdisplay "$VM_NAME" 2>/dev/null)"
    echo "   - O usar: ./setup_web_vnc.sh"
else
    echo "❌ VM necesita atención"
    echo "🔧 Opciones:"
    echo "   1. Recrear VM: ./create_working_vm.sh"
    echo "   2. Verificar archivos: ls -la ~/VMs/"
    echo "   3. Revisar plantillas: ls -la ~/.local/share/vm-templates/"
fi

echo ""
echo "📋 COMANDOS ÚTILES:"
echo "==================="
echo "- Ver VMs: virsh list --all"
echo "- Iniciar VM: virsh start $VM_NAME"
echo "- Conectar VNC: vinagre \$(virsh domdisplay $VM_NAME)"
echo "- Apagar VM: virsh shutdown $VM_NAME"
