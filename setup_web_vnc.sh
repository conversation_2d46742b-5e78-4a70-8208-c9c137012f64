#!/bin/bash

# Script para configurar acceso web a la VM usando noVNC

echo "🌐 CONFIGURANDO ACCESO WEB A LA VM"
echo "=================================="

# Configurar libvirt de usuario
export LIBVIRT_DEFAULT_URI="qemu:///session"

echo "1. Verificando estado de la VM..."
VM_STATUS=$(virsh list | grep "Ubuntu-Dev" | awk '{print $3}')
if [ "$VM_STATUS" = "ejecutando" ]; then
    echo "✅ VM Ubuntu-Dev está ejecutándose"
else
    echo "⚠️  Iniciando VM..."
    virsh start Ubuntu-Dev
    sleep 3
fi

echo ""
echo "2. Obteniendo información de VNC..."
VNC_DISPLAY=$(virsh domdisplay Ubuntu-Dev 2>/dev/null)
echo "Display VNC: $VNC_DISPLAY"

# Extraer puerto VNC
if [[ $VNC_DISPLAY =~ vnc://127\.0\.0\.1:([0-9]+) ]]; then
    VNC_PORT=$((5900 + ${BASH_REMATCH[1]}))
else
    VNC_PORT=5900
fi

echo "Puerto VNC: $VNC_PORT"

echo ""
echo "3. Verificando conexión VNC..."
if ss -tlnp | grep -q ":$VNC_PORT"; then
    echo "✅ VNC está escuchando en puerto $VNC_PORT"
else
    echo "⚠️  VNC no detectado en puerto $VNC_PORT, probando puerto 5900..."
    VNC_PORT=5900
fi

echo ""
echo "4. Creando página web para acceso VNC..."

# Crear directorio para el servidor web
mkdir -p ~/vm-web-access

# Crear página HTML simple con instrucciones
cat > ~/vm-web-access/index.html << EOF
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acceso a VM Ubuntu-Dev</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; }
        .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .method { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        code { background: #f8f9fa; padding: 2px 5px; border-radius: 3px; }
        .button { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖥️ Acceso a VM Ubuntu-Dev</h1>
        
        <div class="status success">
            <strong>✅ VM Estado:</strong> Ejecutándose<br>
            <strong>🔗 VNC Display:</strong> $VNC_DISPLAY<br>
            <strong>🔌 Puerto VNC:</strong> $VNC_PORT
        </div>

        <h2>🎯 Métodos de Conexión</h2>

        <div class="method">
            <h3>1. Cliente VNC Nativo (Recomendado)</h3>
            <div class="info">
                <p>Instala un cliente VNC para mejor rendimiento:</p>
                <code>sudo pacman -S vinagre</code><br>
                <code>sudo pacman -S virt-viewer</code><br>
                <code>sudo pacman -S remmina</code>
            </div>
            <p><strong>Conexión:</strong></p>
            <ul>
                <li>Host: <code>127.0.0.1</code></li>
                <li>Puerto: <code>$VNC_PORT</code></li>
                <li>URL: <code>$VNC_DISPLAY</code></li>
            </ul>
        </div>

        <div class="method">
            <h3>2. Cliente VNC Web (noVNC)</h3>
            <div class="warning">
                <p>Requiere configuración adicional de websockify</p>
            </div>
            <p>Para usar noVNC necesitas instalar websockify:</p>
            <code>pip install websockify</code><br>
            <code>websockify 6080 127.0.0.1:$VNC_PORT</code>
        </div>

        <div class="method">
            <h3>3. Acceso Directo con Comandos</h3>
            <div class="info">
                <p>Comandos útiles para gestionar la VM:</p>
            </div>
            <ul>
                <li>Ver estado: <code>virsh list</code></li>
                <li>Conectar: <code>virt-viewer Ubuntu-Dev</code></li>
                <li>Apagar: <code>virsh shutdown Ubuntu-Dev</code></li>
                <li>Forzar apagado: <code>virsh destroy Ubuntu-Dev</code></li>
            </ul>
        </div>

        <h2>🚀 Próximos Pasos</h2>
        <ol>
            <li>Instala un cliente VNC (vinagre es ligero y fácil)</li>
            <li>Conecta usando la información de arriba</li>
            <li>Deberías ver la pantalla de arranque de Lubuntu</li>
            <li>Procede con la instalación de Ubuntu/Lubuntu</li>
        </ol>

        <div class="status info">
            <strong>💡 Tip:</strong> Si ves pantalla negra, espera 1-2 minutos. La VM puede estar arrancando.
        </div>
    </div>
</body>
</html>
EOF

echo "✅ Página web creada en: ~/vm-web-access/index.html"

echo ""
echo "5. Iniciando servidor web local..."
cd ~/vm-web-access
python3 -m http.server 8080 &
WEB_PID=$!

echo "✅ Servidor web iniciado en puerto 8080"

echo ""
echo "🎉 CONFIGURACIÓN COMPLETADA"
echo "==========================="
echo ""
echo "🌐 ACCESO WEB: http://localhost:8080"
echo "🖥️  VNC DIRECTO: $VNC_DISPLAY"
echo "🔌 PUERTO VNC: $VNC_PORT"
echo ""
echo "🎯 RECOMENDACIÓN:"
echo "1. Abre http://localhost:8080 en tu navegador"
echo "2. Sigue las instrucciones para instalar un cliente VNC"
echo "3. O usa la información de conexión directa"
echo ""
echo "Para detener el servidor web: kill $WEB_PID"
