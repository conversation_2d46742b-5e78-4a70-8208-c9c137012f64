#!/bin/bash

# Script para iniciar VM con configuración de debug para primer arranque

VM_NAME="Ubuntu-Dev"

echo "🔧 INICIANDO VM CON CONFIGURACIÓN DE DEBUG"
echo "=========================================="

# Verificar que la VM existe
if ! virsh list --all | grep -q "$VM_NAME"; then
    echo "❌ Error: VM '$VM_NAME' no existe"
    exit 1
fi

# Asegurar que la VM está apagada
echo "Verificando estado de la VM..."
if virsh list | grep -q "$VM_NAME"; then
    echo "Apagando VM..."
    virsh destroy "$VM_NAME"
    sleep 2
fi

# Iniciar VM con configuración específica
echo "Iniciando VM con configuración optimizada..."

# Usar virsh start con parámetros específicos
virsh start "$VM_NAME"

if [ $? -eq 0 ]; then
    echo "✅ VM iniciada correctamente"
    echo ""
    echo "📋 Información de conexión:"
    echo "- SPICE: $(virsh domdisplay $VM_NAME)"
    echo "- Estado: $(virsh list | grep $VM_NAME | awk '{print $3}')"
    echo ""
    echo "🔍 Monitoreo en tiempo real:"
    echo "Para ver logs: journalctl -f | grep qemu"
    echo "Para ver estado: watch 'virsh list; echo; virsh dominfo $VM_NAME'"
    echo ""
    echo "⏱️  Espera 30-60 segundos para que aparezca la pantalla de Lubuntu"
    echo "Si sigue en negro, presiona ENTER en GNOME Boxes"
else
    echo "❌ Error al iniciar la VM"
    exit 1
fi
