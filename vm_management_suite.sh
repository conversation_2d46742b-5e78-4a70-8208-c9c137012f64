#!/bin/bash

# Suite completa de gestión de VMs para sistema con 40GB RAM
# Sistema avanzado de gestión automática y optimización

echo "🛠️ CREANDO SUITE DE GESTIÓN AVANZADA DE VMs"
echo "==========================================="

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

show_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

show_header "CREANDO SCRIPT PRINCIPAL DE GESTIÓN"

# Script principal de gestión de VMs
cat > ~/.local/bin/vm-suite << 'EOF'
#!/bin/bash

# Suite completa de gestión de VMs optimizada para 40GB RAM
# Gestión automática, monitoreo y optimización de recursos

VERSION="1.0"
CONFIG_DIR="$HOME/.config/vm-suite"
LOG_FILE="$CONFIG_DIR/vm-suite.log"

# Crear directorio de configuración
mkdir -p "$CONFIG_DIR"

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Función de logging
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    VM MANAGEMENT SUITE v$VERSION                    ║"
    echo "║              Optimizado para sistemas con 40GB RAM              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

show_main_menu() {
    echo -e "${CYAN}Opciones disponibles:${NC}"
    echo "  1. Gestión de VMs"
    echo "  2. Monitoreo del sistema"
    echo "  3. Optimización automática"
    echo "  4. Gestión de snapshots"
    echo "  5. Gestión de redes"
    echo "  6. Gestión de almacenamiento"
    echo "  7. Configuración del sistema"
    echo "  8. Logs y diagnósticos"
    echo "  9. Ayuda"
    echo "  0. Salir"
    echo ""
}

# Gestión de VMs
vm_management() {
    echo -e "${BLUE}=== GESTIÓN DE VMs ===${NC}"
    echo "1. Listar VMs"
    echo "2. Crear VM nueva"
    echo "3. Iniciar VM"
    echo "4. Detener VM"
    echo "5. Reiniciar VM"
    echo "6. Eliminar VM"
    echo "7. Clonar VM"
    echo "8. Información de VM"
    echo "0. Volver al menú principal"
    echo ""
    
    read -p "Selecciona una opción: " vm_option
    
    case $vm_option in
        1) list_vms ;;
        2) create_vm_wizard ;;
        3) start_vm_interactive ;;
        4) stop_vm_interactive ;;
        5) restart_vm_interactive ;;
        6) delete_vm_interactive ;;
        7) clone_vm_interactive ;;
        8) vm_info_interactive ;;
        0) return ;;
        *) echo "Opción inválida" ;;
    esac
}

list_vms() {
    echo -e "${CYAN}VMs existentes:${NC}"
    virsh list --all --name | while read vm; do
        if [ -n "$vm" ]; then
            state=$(virsh domstate "$vm" 2>/dev/null)
            case $state in
                "running") echo -e "  ${GREEN}●${NC} $vm (ejecutándose)" ;;
                "shut off") echo -e "  ${RED}●${NC} $vm (apagada)" ;;
                "paused") echo -e "  ${YELLOW}●${NC} $vm (pausada)" ;;
                *) echo -e "  ${BLUE}●${NC} $vm ($state)" ;;
            esac
        fi
    done
}

create_vm_wizard() {
    echo -e "${BLUE}=== ASISTENTE DE CREACIÓN DE VM ===${NC}"
    
    # Mostrar plantillas disponibles
    echo "Plantillas disponibles:"
    echo "1. Desarrollo Ligero (4GB RAM, 2 cores)"
    echo "2. Desarrollo Pesado (8GB RAM, 4 cores)"
    echo "3. Servidor (12GB RAM, 6 cores)"
    echo "4. Alto Rendimiento (16GB RAM, 8 cores)"
    echo "5. Testing (6GB RAM, 3 cores)"
    
    read -p "Selecciona plantilla (1-5): " template_choice
    
    case $template_choice in
        1) template="desarrollo-ligero" ;;
        2) template="desarrollo-pesado" ;;
        3) template="servidor" ;;
        4) template="alto-rendimiento" ;;
        5) template="testing" ;;
        *) echo "Opción inválida"; return ;;
    esac
    
    read -p "Nombre de la VM: " vm_name
    read -p "Tamaño del disco (GB): " disk_size
    read -p "Ruta del ISO (opcional): " iso_path
    
    if [ -n "$vm_name" ] && [ -n "$disk_size" ]; then
        echo "Creando VM '$vm_name' con plantilla '$template'..."
        vm-manager create "$template" "$vm_name" "$disk_size" "$iso_path"
        log_message "VM creada: $vm_name ($template, ${disk_size}GB)"
    else
        echo "Error: Faltan parámetros"
    fi
}

start_vm_interactive() {
    list_vms
    read -p "Nombre de la VM a iniciar: " vm_name
    if [ -n "$vm_name" ]; then
        virsh start "$vm_name"
        log_message "VM iniciada: $vm_name"
    fi
}

stop_vm_interactive() {
    list_vms
    read -p "Nombre de la VM a detener: " vm_name
    if [ -n "$vm_name" ]; then
        virsh shutdown "$vm_name"
        log_message "VM detenida: $vm_name"
    fi
}

restart_vm_interactive() {
    list_vms
    read -p "Nombre de la VM a reiniciar: " vm_name
    if [ -n "$vm_name" ]; then
        virsh reboot "$vm_name"
        log_message "VM reiniciada: $vm_name"
    fi
}

delete_vm_interactive() {
    list_vms
    read -p "Nombre de la VM a eliminar: " vm_name
    if [ -n "$vm_name" ]; then
        echo -e "${RED}¿Estás seguro de eliminar '$vm_name'? (s/N)${NC}"
        read -r confirm
        if [ "$confirm" = "s" ] || [ "$confirm" = "S" ]; then
            virsh destroy "$vm_name" 2>/dev/null
            virsh undefine "$vm_name"
            echo "VM '$vm_name' eliminada"
            log_message "VM eliminada: $vm_name"
        fi
    fi
}

clone_vm_interactive() {
    list_vms
    read -p "Nombre de la VM a clonar: " source_vm
    read -p "Nombre de la nueva VM: " new_vm
    if [ -n "$source_vm" ] && [ -n "$new_vm" ]; then
        virt-clone --original "$source_vm" --name "$new_vm" --auto-clone
        log_message "VM clonada: $source_vm -> $new_vm"
    fi
}

vm_info_interactive() {
    list_vms
    read -p "Nombre de la VM: " vm_name
    if [ -n "$vm_name" ]; then
        virsh dominfo "$vm_name"
    fi
}

# Monitoreo del sistema
system_monitoring() {
    echo -e "${BLUE}=== MONITOREO DEL SISTEMA ===${NC}"
    
    # Información general del sistema
    echo -e "${CYAN}Información del sistema:${NC}"
    echo "Memoria total: $(free -h | awk '/^Mem:/ {print $2}')"
    echo "Memoria libre: $(free -h | awk '/^Mem:/ {print $7}')"
    echo "Memoria usada: $(free -h | awk '/^Mem:/ {print $3}')"
    echo "Swap total: $(free -h | awk '/^Swap:/ {print $2}')"
    echo "CPU cores: $(nproc)"
    echo "Load average: $(uptime | awk -F'load average:' '{print $2}')"
    
    echo ""
    echo -e "${CYAN}VMs activas y su uso de recursos:${NC}"
    vm-monitor
    
    echo ""
    echo -e "${CYAN}Uso de almacenamiento:${NC}"
    df -h /home/<USER>/VMs | tail -1
    
    read -p "Presiona Enter para continuar..."
}

# Optimización automática
auto_optimization() {
    echo -e "${BLUE}=== OPTIMIZACIÓN AUTOMÁTICA ===${NC}"
    echo "1. Optimizar todas las VMs apagadas"
    echo "2. Limpiar snapshots antiguos"
    echo "3. Compactar discos de VMs"
    echo "4. Optimizar configuración del sistema"
    echo "0. Volver"
    
    read -p "Selecciona una opción: " opt_choice
    
    case $opt_choice in
        1) optimize_all_vms ;;
        2) cleanup_old_snapshots ;;
        3) compact_vm_disks ;;
        4) optimize_system_config ;;
        0) return ;;
        *) echo "Opción inválida" ;;
    esac
}

optimize_all_vms() {
    echo "Optimizando todas las VMs apagadas..."
    virsh list --state-shutoff --name | while read vm; do
        if [ -n "$vm" ]; then
            echo "Optimizando VM: $vm"
            optimize-vm-disk "$vm"
        fi
    done
    log_message "Optimización automática completada"
}

cleanup_old_snapshots() {
    echo "Limpiando snapshots antiguos..."
    # Implementar lógica de limpieza de snapshots
    log_message "Limpieza de snapshots completada"
}

compact_vm_disks() {
    echo "Compactando discos de VMs..."
    virsh list --state-shutoff --name | while read vm; do
        if [ -n "$vm" ]; then
            echo "Compactando disco de VM: $vm"
            optimize-vm-disk "$vm"
        fi
    done
}

optimize_system_config() {
    echo "Optimizando configuración del sistema..."
    sudo sysctl -p /etc/sysctl.d/99-memory-optimization.conf
    sudo sysctl -p /etc/sysctl.d/99-vm-network.conf
    echo "Configuración optimizada"
}

# Función principal
main() {
    show_banner
    
    while true; do
        show_main_menu
        read -p "Selecciona una opción: " choice
        
        case $choice in
            1) vm_management ;;
            2) system_monitoring ;;
            3) auto_optimization ;;
            4) echo "Gestión de snapshots - En desarrollo" ;;
            5) echo "Gestión de redes - En desarrollo" ;;
            6) echo "Gestión de almacenamiento - En desarrollo" ;;
            7) echo "Configuración del sistema - En desarrollo" ;;
            8) echo "Logs: $LOG_FILE" ;;
            9) echo "Ayuda - Consulta la documentación" ;;
            0) echo "¡Hasta luego!"; exit 0 ;;
            *) echo -e "${RED}Opción inválida${NC}" ;;
        esac
        
        echo ""
        read -p "Presiona Enter para continuar..."
        clear
    done
}

# Verificar dependencias
if ! command -v virsh &> /dev/null; then
    echo -e "${RED}Error: libvirt no está instalado${NC}"
    exit 1
fi

# Ejecutar función principal
main "$@"
EOF

chmod +x ~/.local/bin/vm-suite
show_message "Suite principal de gestión creada: vm-suite"

show_header "CREANDO SCRIPTS DE AUTOMATIZACIÓN"

# Script de backup automático
cat > ~/.local/bin/vm-backup << 'EOF'
#!/bin/bash

# Script de backup automático de VMs
# Crea backups comprimidos de VMs importantes

BACKUP_DIR="/home/<USER>/VMs/backups"
LOG_FILE="$HOME/.config/vm-suite/backup.log"

mkdir -p "$BACKUP_DIR"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

backup_vm() {
    local vm_name="$1"
    local backup_file="$BACKUP_DIR/${vm_name}_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    log_message "Iniciando backup de VM: $vm_name"
    
    # Verificar que la VM esté apagada
    if [ "$(virsh domstate "$vm_name")" != "shut off" ]; then
        log_message "Error: VM $vm_name debe estar apagada para backup"
        return 1
    fi
    
    # Obtener archivos de la VM
    local vm_xml="/tmp/${vm_name}.xml"
    local disk_path=$(virsh domblklist "$vm_name" | grep vda | awk '{print $2}')
    
    # Exportar configuración XML
    virsh dumpxml "$vm_name" > "$vm_xml"
    
    # Crear backup comprimido
    tar -czf "$backup_file" -C "$(dirname "$disk_path")" "$(basename "$disk_path")" -C /tmp "$(basename "$vm_xml")"
    
    # Limpiar archivo temporal
    rm -f "$vm_xml"
    
    log_message "Backup completado: $backup_file"
}

# Backup de todas las VMs apagadas
if [ "$1" = "--all" ]; then
    virsh list --state-shutoff --name | while read vm; do
        if [ -n "$vm" ]; then
            backup_vm "$vm"
        fi
    done
elif [ -n "$1" ]; then
    backup_vm "$1"
else
    echo "Uso: vm-backup <vm_name> | --all"
fi
EOF

chmod +x ~/.local/bin/vm-backup
show_message "Script de backup automático creado: vm-backup"

# Script de limpieza automática
cat > ~/.local/bin/vm-cleanup << 'EOF'
#!/bin/bash

# Script de limpieza automática del sistema de VMs
# Limpia archivos temporales, logs antiguos y optimiza el sistema

LOG_FILE="$HOME/.config/vm-suite/cleanup.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

echo "🧹 Iniciando limpieza automática del sistema de VMs..."

# Limpiar logs antiguos de libvirt
log_message "Limpiando logs antiguos de libvirt"
sudo find /var/log/libvirt -name "*.log" -mtime +7 -delete 2>/dev/null || true

# Limpiar archivos temporales de VMs
log_message "Limpiando archivos temporales"
find /tmp -name "*.xml" -mtime +1 -delete 2>/dev/null || true

# Limpiar backups antiguos (más de 30 días)
log_message "Limpiando backups antiguos"
find "/home/<USER>/VMs/backups" -name "*.tar.gz" -mtime +30 -delete 2>/dev/null || true

# Optimizar pools de almacenamiento
log_message "Refrescando pools de almacenamiento"
for pool in vm-storage ssd-pool dev-pool test-pool; do
    virsh pool-refresh "$pool" 2>/dev/null || true
done

# Limpiar cache del sistema
log_message "Limpiando cache del sistema"
sync
echo 1 | sudo tee /proc/sys/vm/drop_caches > /dev/null

log_message "Limpieza automática completada"
echo "✓ Limpieza completada. Ver log: $LOG_FILE"
EOF

chmod +x ~/.local/bin/vm-cleanup
show_message "Script de limpieza automática creado: vm-cleanup"

show_header "CONFIGURANDO AUTOMATIZACIÓN"

# Crear tareas cron para automatización
show_info "Configurando tareas automáticas..."

# Crear archivo de cron para el usuario
(crontab -l 2>/dev/null; echo "# VM Management Suite - Tareas automáticas") | crontab -
(crontab -l 2>/dev/null; echo "0 2 * * 0 $HOME/.local/bin/vm-cleanup") | crontab -
(crontab -l 2>/dev/null; echo "0 3 * * 0 $HOME/.local/bin/vm-backup --all") | crontab -

show_message "Tareas automáticas configuradas (limpieza semanal, backup semanal)"

show_header "CREANDO DOCUMENTACIÓN"

# Crear documentación del sistema
cat > ~/.config/vm-suite/README.md << 'EOF'
# VM Management Suite

Sistema completo de gestión de máquinas virtuales optimizado para sistemas con 40GB RAM.

## Scripts Principales

### vm-suite
Suite principal de gestión con interfaz interactiva.
```bash
vm-suite
```

### vm-manager
Gestión básica de VMs desde línea de comandos.
```bash
vm-manager create desarrollo-pesado Ubuntu-Dev 50 ~/Downloads/ubuntu.iso
vm-manager start Ubuntu-Dev
vm-manager list-vms
```

### vm-monitor
Monitoreo de recursos de VMs.
```bash
vm-monitor
```

### vm-backup
Backup automático de VMs.
```bash
vm-backup Ubuntu-Dev          # Backup de una VM específica
vm-backup --all              # Backup de todas las VMs apagadas
```

### vm-cleanup
Limpieza automática del sistema.
```bash
vm-cleanup
```

### optimize-vm-disk
Optimización de discos de VMs.
```bash
optimize-vm-disk Ubuntu-Dev
```

## Plantillas Disponibles

- **desarrollo-ligero**: 4GB RAM, 2 cores (desarrollo básico)
- **desarrollo-pesado**: 8GB RAM, 4 cores (IDEs pesados, compilación)
- **servidor**: 12GB RAM, 6 cores (servicios, bases de datos)
- **alto-rendimiento**: 16GB RAM, 8 cores (máximo rendimiento)
- **testing**: 6GB RAM, 3 cores (pruebas, laboratorio)

## Redes Virtuales

- **vm-optimized**: Red principal optimizada (192.168.100.x)
- **vm-nat-dev**: Red NAT para desarrollo (192.168.200.x)
- **vm-bridge-srv**: Red bridge para servidores
- **vm-isolated-test**: Red aislada para testing (10.0.0.x)

## Pools de Almacenamiento

- **vm-storage**: Pool principal
- **ssd-pool**: Pool SSD para alto rendimiento
- **dev-pool**: Pool para desarrollo
- **test-pool**: Pool para testing

## Automatización

- Limpieza automática: Domingos a las 2:00 AM
- Backup automático: Domingos a las 3:00 AM
- Logs en: ~/.config/vm-suite/

## Configuración Optimizada

El sistema está configurado para aprovechar al máximo los 40GB de RAM:
- ZRAM configurado (8GB)
- Swappiness optimizado (1)
- Huge pages habilitadas
- Configuraciones de red optimizadas
- Variables de entorno para aplicaciones pesadas
EOF

show_message "Documentación creada en ~/.config/vm-suite/README.md"

show_header "CONFIGURACIÓN COMPLETADA"
show_info "Suite de gestión completa instalada"
show_info "Script principal: vm-suite"
show_info "Documentación: ~/.config/vm-suite/README.md"
show_info "Logs: ~/.config/vm-suite/"

echo ""
echo -e "${GREEN}🎉 SUITE DE GESTIÓN DE VMs COMPLETADA${NC}"
echo -e "${BLUE}Ejecuta 'vm-suite' para comenzar${NC}"
