#!/bin/bash

# Script para crear plantillas de VMs optimizadas para 40GB RAM
# Diferentes configuraciones según el uso previsto

echo "🖥️ CREANDO PLANTILLAS DE VMs OPTIMIZADAS PARA 40GB RAM"
echo "===================================================="

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

show_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

show_template() {
    echo -e "${CYAN}📋${NC} $1"
}

# Crear directorio para plantillas
mkdir -p ~/.local/share/vm-templates
mkdir -p ~/.local/bin

show_header "PLANTILLAS DE VMs PARA DIFERENTES USOS"

# 1. Plantilla VM de Desarrollo Ligero (4GB RAM, 2 cores)
show_template "Creando plantilla: Desarrollo Ligero (4GB RAM, 2 cores)"

cat > ~/.local/share/vm-templates/desarrollo-ligero.xml << 'EOF'
<domain type='kvm'>
  <name>TEMPLATE-Desarrollo-Ligero</name>
  <memory unit='MiB'>4096</memory>
  <currentMemory unit='MiB'>4096</currentMemory>
  <vcpu placement='static'>2</vcpu>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <interface type='network'>
      <source network='vm-optimized'/>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'/>
    <video>
      <model type='qxl' ram='65536' vram='65536' vgamem='16384' heads='1'/>
    </video>
    <memballoon model='virtio'/>
  </devices>
</domain>
EOF

# 2. Plantilla VM de Desarrollo Pesado (8GB RAM, 4 cores)
show_template "Creando plantilla: Desarrollo Pesado (8GB RAM, 4 cores)"

cat > ~/.local/share/vm-templates/desarrollo-pesado.xml << 'EOF'
<domain type='kvm'>
  <name>TEMPLATE-Desarrollo-Pesado</name>
  <memory unit='MiB'>8192</memory>
  <currentMemory unit='MiB'>8192</currentMemory>
  <vcpu placement='static'>4</vcpu>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <interface type='network'>
      <source network='vm-optimized'/>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'/>
    <video>
      <model type='qxl' ram='131072' vram='131072' vgamem='32768' heads='1'/>
    </video>
    <memballoon model='virtio'/>
  </devices>
</domain>
EOF

# 3. Plantilla VM de Servidor (12GB RAM, 6 cores)
show_template "Creando plantilla: Servidor (12GB RAM, 6 cores)"

cat > ~/.local/share/vm-templates/servidor.xml << 'EOF'
<domain type='kvm'>
  <name>TEMPLATE-Servidor</name>
  <memory unit='MiB'>12288</memory>
  <currentMemory unit='MiB'>12288</currentMemory>
  <vcpu placement='static'>6</vcpu>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <interface type='network'>
      <source network='vm-optimized'/>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'/>
    <video>
      <model type='qxl' ram='65536' vram='65536' vgamem='16384' heads='1'/>
    </video>
    <memballoon model='virtio'/>
  </devices>
</domain>
EOF

# 4. Plantilla VM de Alto Rendimiento (16GB RAM, 8 cores)
show_template "Creando plantilla: Alto Rendimiento (16GB RAM, 8 cores)"

cat > ~/.local/share/vm-templates/alto-rendimiento.xml << 'EOF'
<domain type='kvm'>
  <name>TEMPLATE-Alto-Rendimiento</name>
  <memory unit='MiB'>16384</memory>
  <currentMemory unit='MiB'>16384</currentMemory>
  <vcpu placement='static'>8</vcpu>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <interface type='network'>
      <source network='vm-optimized'/>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'/>
    <video>
      <model type='qxl' ram='262144' vram='262144' vgamem='65536' heads='2'/>
    </video>
    <memballoon model='virtio'/>
  </devices>
</domain>
EOF

# 5. Plantilla VM de Testing/Laboratorio (6GB RAM, 3 cores)
show_template "Creando plantilla: Testing/Laboratorio (6GB RAM, 3 cores)"

cat > ~/.local/share/vm-templates/testing.xml << 'EOF'
<domain type='kvm'>
  <name>TEMPLATE-Testing</name>
  <memory unit='MiB'>6144</memory>
  <currentMemory unit='MiB'>6144</currentMemory>
  <vcpu placement='static'>3</vcpu>
  <features>
    <acpi/>
    <apic/>
    <hyperv>
      <relaxed state='on'/>
      <vapic state='on'/>
      <spinlocks state='on' retries='8191'/>
    </hyperv>
    <vmport state='off'/>
  </features>
  <cpu mode='host-passthrough' check='none'/>
  <clock offset='localtime'>
    <timer name='rtc' tickpolicy='catchup'/>
    <timer name='pit' tickpolicy='delay'/>
    <timer name='hpet' present='no'/>
    <timer name='hypervclock' present='yes'/>
  </clock>
  <devices>
    <emulator>/usr/bin/qemu-system-x86_64</emulator>
    <disk type='file' device='disk'>
      <driver name='qemu' type='qcow2' cache='writeback'/>
      <source file='DISK_PATH'/>
      <target dev='vda' bus='virtio'/>
    </disk>
    <interface type='network'>
      <source network='vm-optimized'/>
      <model type='virtio'/>
    </interface>
    <graphics type='spice' autoport='yes'/>
    <video>
      <model type='qxl' ram='65536' vram='65536' vgamem='16384' heads='1'/>
    </video>
    <memballoon model='virtio'/>
  </devices>
</domain>
EOF

show_message "Plantillas XML creadas en ~/.local/share/vm-templates/"

show_header "CREANDO SCRIPT DE GESTIÓN DE PLANTILLAS"

# Script principal de gestión de plantillas
cat > ~/.local/bin/vm-manager << 'EOF'
#!/bin/bash

# Gestor de plantillas de VMs optimizadas para 40GB RAM
# Permite crear VMs basadas en plantillas predefinidas

TEMPLATES_DIR="$HOME/.local/share/vm-templates"
VMS_DIR="/home/<USER>/VMs"

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

show_usage() {
    echo "Uso: vm-manager <comando> [opciones]"
    echo ""
    echo "Comandos disponibles:"
    echo "  list-templates    - Listar plantillas disponibles"
    echo "  create <template> <nombre> <disco_gb> [iso_path] - Crear VM desde plantilla"
    echo "  list-vms         - Listar VMs existentes"
    echo "  start <vm>       - Iniciar VM"
    echo "  stop <vm>        - Detener VM"
    echo "  delete <vm>      - Eliminar VM"
    echo "  info <vm>        - Mostrar información de VM"
    echo ""
    echo "Plantillas disponibles:"
    echo "  desarrollo-ligero  - 4GB RAM, 2 cores (desarrollo básico)"
    echo "  desarrollo-pesado  - 8GB RAM, 4 cores (IDEs pesados, compilación)"
    echo "  servidor          - 12GB RAM, 6 cores (servicios, bases de datos)"
    echo "  alto-rendimiento  - 16GB RAM, 8 cores (máximo rendimiento)"
    echo "  testing           - 6GB RAM, 3 cores (pruebas, laboratorio)"
    echo ""
    echo "Ejemplos:"
    echo "  vm-manager create desarrollo-pesado Ubuntu-Dev 50 ~/Downloads/ubuntu.iso"
    echo "  vm-manager start Ubuntu-Dev"
    echo "  vm-manager list-vms"
}

list_templates() {
    echo -e "${BLUE}Plantillas disponibles:${NC}"
    for template in "$TEMPLATES_DIR"/*.xml; do
        if [ -f "$template" ]; then
            basename "$template" .xml | sed 's/^/  - /'
        fi
    done
}

create_vm() {
    local template="$1"
    local vm_name="$2"
    local disk_size="$3"
    local iso_path="$4"
    
    if [ -z "$template" ] || [ -z "$vm_name" ] || [ -z "$disk_size" ]; then
        echo -e "${RED}Error: Faltan parámetros${NC}"
        show_usage
        return 1
    fi
    
    local template_file="$TEMPLATES_DIR/${template}.xml"
    if [ ! -f "$template_file" ]; then
        echo -e "${RED}Error: Plantilla '$template' no encontrada${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Creando VM '$vm_name' desde plantilla '$template'${NC}"
    
    # Crear disco
    local disk_path="$VMS_DIR/${vm_name}.qcow2"
    echo "Creando disco de ${disk_size}GB..."
    qemu-img create -f qcow2 "$disk_path" "${disk_size}G"
    
    # Crear configuración XML
    local vm_xml="/tmp/${vm_name}.xml"
    sed "s/TEMPLATE-[^<]*/${vm_name}/g; s|DISK_PATH|${disk_path}|g" "$template_file" > "$vm_xml"
    
    # Agregar ISO si se proporciona
    if [ -n "$iso_path" ] && [ -f "$iso_path" ]; then
        echo "Agregando ISO: $iso_path"
        # Insertar configuración de CDROM antes del cierre de devices
        sed -i "/<\/devices>/i\\    <disk type='file' device='cdrom'>\\n      <driver name='qemu' type='raw'/>\\n      <source file='${iso_path}'/>\\n      <target dev='hdc' bus='ide'/>\\n      <readonly/>\\n    </disk>" "$vm_xml"
    fi
    
    # Definir VM
    virsh define "$vm_xml"
    
    echo -e "${GREEN}✓ VM '$vm_name' creada exitosamente${NC}"
    echo "Disco: $disk_path"
    echo "Configuración: $vm_xml"
    
    # Limpiar archivo temporal
    rm -f "$vm_xml"
}

list_vms() {
    echo -e "${BLUE}VMs existentes:${NC}"
    virsh list --all --name | while read vm; do
        if [ -n "$vm" ]; then
            local state=$(virsh domstate "$vm" 2>/dev/null)
            echo "  - $vm ($state)"
        fi
    done
}

start_vm() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Iniciando VM '$vm_name'${NC}"
    virsh start "$vm_name"
}

stop_vm() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Deteniendo VM '$vm_name'${NC}"
    virsh shutdown "$vm_name"
}

delete_vm() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}¿Estás seguro de eliminar la VM '$vm_name'? (s/N)${NC}"
    read -r confirm
    if [ "$confirm" = "s" ] || [ "$confirm" = "S" ]; then
        virsh destroy "$vm_name" 2>/dev/null
        virsh undefine "$vm_name"
        rm -f "$VMS_DIR/${vm_name}.qcow2"
        echo -e "${GREEN}✓ VM '$vm_name' eliminada${NC}"
    else
        echo "Operación cancelada"
    fi
}

vm_info() {
    local vm_name="$1"
    if [ -z "$vm_name" ]; then
        echo -e "${RED}Error: Especifica el nombre de la VM${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Información de VM '$vm_name':${NC}"
    virsh dominfo "$vm_name"
}

# Comando principal
case "$1" in
    list-templates)
        list_templates
        ;;
    create)
        create_vm "$2" "$3" "$4" "$5"
        ;;
    list-vms)
        list_vms
        ;;
    start)
        start_vm "$2"
        ;;
    stop)
        stop_vm "$2"
        ;;
    delete)
        delete_vm "$2"
        ;;
    info)
        vm_info "$2"
        ;;
    *)
        show_usage
        ;;
esac
EOF

chmod +x ~/.local/bin/vm-manager
show_message "Script de gestión creado: vm-manager"

show_header "CONFIGURACIÓN COMPLETADA"
show_info "Plantillas creadas para diferentes tipos de uso"
show_info "Script de gestión disponible: vm-manager"
show_info "Directorio de VMs: /home/<USER>/VMs"

echo ""
echo -e "${GREEN}🎉 PLANTILLAS DE VMs CREADAS${NC}"
echo -e "${BLUE}Usa 'vm-manager' para gestionar tus VMs${NC}"
