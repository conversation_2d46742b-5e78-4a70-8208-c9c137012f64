#!/bin/bash

# Script de optimización VMware para sistema con 40GB RAM
# Configura VMware Workstation Pro para máximo rendimiento

echo "🖥️ OPTIMIZANDO VMWARE WORKSTATION PRO PARA 40GB RAM"
echo "=================================================="

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

show_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

show_message() {
    echo -e "${GREEN}✓${NC} $1"
}

show_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Verificar que VMware esté instalado
if ! command -v vmware &> /dev/null; then
    echo "❌ VMware Workstation no está instalado"
    exit 1
fi

show_header "CONFIGURANDO VMWARE PARA 40GB RAM"

# Crear directorio de configuración si no existe
mkdir -p ~/.vmware

# Configuración global de VMware optimizada para 40GB RAM
show_info "Creando configuración global optimizada..."

cat > ~/.vmware/preferences << 'EOF'
# Configuración VMware optimizada para 40GB RAM

# Configuración de memoria
prefvmx.defaultVMPath = "/home/<USER>/VMs"
prefvmx.minVmMemPct = "25"
mainMem.useNamedFile = "FALSE"
MemTrimRate = "0"
sched.mem.pshare.enable = "FALSE"
MemAllowAutoScaleDown = "FALSE"
mainMem.backing = "swap"

# Configuración de CPU
monitor.virtual_mmu = "hardware"
monitor.virtual_exec = "hardware"
vhv.enable = "TRUE"
hypervisor.cpuid.v0 = "FALSE"

# Configuración de rendimiento
mainMem.partialLazySave = "FALSE"
mainMem.partialLazyRestore = "FALSE"
prefvmx.useRecommendedLockedMemSize = "TRUE"
prefvmx.minVmMemPct = "25"

# Configuración de red optimizada
vmnet.linkStatePropagation.enable = "TRUE"
ethernet.linkStatePropagation.enable = "TRUE"

# Configuración de disco optimizada
scsi0.virtualDev = "lsisas1068"
disk.EnableUUID = "TRUE"
mainMem.useNamedFile = "FALSE"

# Configuración de gráficos
mks.enable3d = "TRUE"
mks.gl.allowBlacklistedDrivers = "TRUE"
svga.autodetect = "TRUE"
svga.maxWidth = "2560"
svga.maxHeight = "1600"

# Configuración de snapshot optimizada
snapshot.disabled = "FALSE"
snapshot.maxSnapshots = "5"

# Configuración de logging
log.keepOld = "3"
log.rotateSize = "100000"

# Configuración de memoria compartida
sched.mem.pshare.enable = "FALSE"
prefvmx.mru.config = "/home/<USER>/.vmware/preferences"

# Configuración de rendimiento avanzada
monitor_control.restrict_backdoor = "TRUE"
isolation.tools.unity.push.update.disable = "TRUE"
isolation.tools.ghi.autologon.disable = "TRUE"
isolation.tools.hgfs.disable = "FALSE"
EOF

show_message "Configuración global de VMware creada"

# Crear script para configurar VMs individuales
show_info "Creando script de configuración para VMs individuales..."

cat > ~/.vmware/optimize_vm.sh << 'EOF'
#!/bin/bash

# Script para optimizar configuración de VM individual
# Uso: ./optimize_vm.sh <archivo.vmx> <RAM_GB> <CPU_CORES>

if [ $# -ne 3 ]; then
    echo "Uso: $0 <archivo.vmx> <RAM_GB> <CPU_CORES>"
    echo "Ejemplo: $0 Ubuntu.vmx 8 4"
    exit 1
fi

VMX_FILE="$1"
RAM_GB="$2"
CPU_CORES="$3"

if [ ! -f "$VMX_FILE" ]; then
    echo "Error: Archivo VMX no encontrado: $VMX_FILE"
    exit 1
fi

echo "Optimizando VM: $VMX_FILE"
echo "RAM: ${RAM_GB}GB, CPU: ${CPU_CORES} cores"

# Backup del archivo original
cp "$VMX_FILE" "${VMX_FILE}.backup.$(date +%Y%m%d_%H%M%S)"

# Configuraciones de memoria
echo "memsize = \"$(($RAM_GB * 1024))\"" >> "$VMX_FILE"
echo "MemAllowAutoScaleDown = \"FALSE\"" >> "$VMX_FILE"
echo "MemTrimRate = \"0\"" >> "$VMX_FILE"
echo "mainMem.useNamedFile = \"FALSE\"" >> "$VMX_FILE"
echo "sched.mem.pshare.enable = \"FALSE\"" >> "$VMX_FILE"

# Configuraciones de CPU
echo "numvcpus = \"$CPU_CORES\"" >> "$VMX_FILE"
echo "cpuid.coresPerSocket = \"$CPU_CORES\"" >> "$VMX_FILE"
echo "hypervisor.cpuid.v0 = \"FALSE\"" >> "$VMX_FILE"
echo "monitor.virtual_mmu = \"hardware\"" >> "$VMX_FILE"
echo "monitor.virtual_exec = \"hardware\"" >> "$VMX_FILE"
echo "vhv.enable = \"TRUE\"" >> "$VMX_FILE"

# Configuraciones de rendimiento
echo "mainMem.partialLazySave = \"FALSE\"" >> "$VMX_FILE"
echo "mainMem.partialLazyRestore = \"FALSE\"" >> "$VMX_FILE"
echo "mainMem.backing = \"swap\"" >> "$VMX_FILE"

# Configuraciones de disco
echo "scsi0.virtualDev = \"lsisas1068\"" >> "$VMX_FILE"
echo "disk.EnableUUID = \"TRUE\"" >> "$VMX_FILE"

# Configuraciones de gráficos
echo "mks.enable3d = \"TRUE\"" >> "$VMX_FILE"
echo "svga.autodetect = \"TRUE\"" >> "$VMX_FILE"

echo "✓ VM optimizada exitosamente"
EOF

chmod +x ~/.vmware/optimize_vm.sh
show_message "Script de optimización de VMs creado"

# Crear plantillas de configuración para diferentes tipos de VMs
show_header "CREANDO PLANTILLAS DE VMs OPTIMIZADAS"

mkdir -p ~/.vmware/templates

# Plantilla para VM de desarrollo (8GB RAM, 4 cores)
cat > ~/.vmware/templates/desarrollo.vmx << 'EOF'
# Plantilla VM de Desarrollo - 8GB RAM, 4 cores
.encoding = "UTF-8"
config.version = "8"
virtualHW.version = "21"

# Configuración de memoria (8GB)
memsize = "8192"
MemAllowAutoScaleDown = "FALSE"
MemTrimRate = "0"
mainMem.useNamedFile = "FALSE"
sched.mem.pshare.enable = "FALSE"

# Configuración de CPU (4 cores)
numvcpus = "4"
cpuid.coresPerSocket = "4"
hypervisor.cpuid.v0 = "FALSE"
monitor.virtual_mmu = "hardware"
monitor.virtual_exec = "hardware"
vhv.enable = "TRUE"

# Configuración de red
ethernet0.present = "TRUE"
ethernet0.connectionType = "nat"
ethernet0.virtualDev = "vmxnet3"
ethernet0.linkStatePropagation.enable = "TRUE"

# Configuración de disco
scsi0.present = "TRUE"
scsi0.virtualDev = "lsisas1068"
scsi0:0.present = "TRUE"
scsi0:0.fileName = "desarrollo.vmdk"
scsi0:0.deviceType = "scsi-hardDisk"

# Configuración de gráficos
svga.present = "TRUE"
svga.autodetect = "TRUE"
mks.enable3d = "TRUE"
EOF

show_message "Plantilla de desarrollo creada (8GB RAM, 4 cores)"

# Plantilla para VM de servidor (16GB RAM, 6 cores)
cat > ~/.vmware/templates/servidor.vmx << 'EOF'
# Plantilla VM de Servidor - 16GB RAM, 6 cores
.encoding = "UTF-8"
config.version = "8"
virtualHW.version = "21"

# Configuración de memoria (16GB)
memsize = "16384"
MemAllowAutoScaleDown = "FALSE"
MemTrimRate = "0"
mainMem.useNamedFile = "FALSE"
sched.mem.pshare.enable = "FALSE"

# Configuración de CPU (6 cores)
numvcpus = "6"
cpuid.coresPerSocket = "6"
hypervisor.cpuid.v0 = "FALSE"
monitor.virtual_mmu = "hardware"
monitor.virtual_exec = "hardware"
vhv.enable = "TRUE"

# Configuración de red
ethernet0.present = "TRUE"
ethernet0.connectionType = "bridged"
ethernet0.virtualDev = "vmxnet3"
ethernet0.linkStatePropagation.enable = "TRUE"

# Configuración de disco
scsi0.present = "TRUE"
scsi0.virtualDev = "lsisas1068"
scsi0:0.present = "TRUE"
scsi0:0.fileName = "servidor.vmdk"
scsi0:0.deviceType = "scsi-hardDisk"

# Configuración de gráficos
svga.present = "TRUE"
svga.autodetect = "TRUE"
mks.enable3d = "FALSE"
EOF

show_message "Plantilla de servidor creada (16GB RAM, 6 cores)"

show_header "CONFIGURACIÓN COMPLETADA"
show_info "Configuraciones creadas en ~/.vmware/"
show_info "Plantillas disponibles en ~/.vmware/templates/"
show_info "Script de optimización: ~/.vmware/optimize_vm.sh"

echo ""
echo -e "${GREEN}🎉 VMWARE OPTIMIZADO PARA 40GB RAM${NC}"
echo -e "${BLUE}Reinicia VMware para aplicar todas las configuraciones${NC}"
